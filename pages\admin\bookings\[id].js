import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import EnhancedBookingDetails from '@/components/admin/EnhancedBookingDetails';
import BookingForm from '@/components/admin/BookingForm';
import Modal from '@/components/admin/Modal';
import { authenticatedFetch } from '@/lib/auth-utils';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/BookingDetailPage.module.css';

/**
 * Individual Booking Detail Page
 *
 * Displays detailed information about a specific booking
 * Allows editing, status updates, and viewing customer history
 */
export default function BookingDetailPage() {
  const router = useRouter();
  const { id: bookingId } = router.query;

  const [booking, setBooking] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [updating, setUpdating] = useState(false);

  // Fetch booking data when component mounts or ID changes
  useEffect(() => {
    if (bookingId) {
      fetchBooking();
    }
  }, [bookingId]);

  const fetchBooking = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching booking:', bookingId);

      const data = await authenticatedFetch(`/api/admin/bookings/${bookingId}`);

      if (data.booking) {
        setBooking(data.booking);
        console.log('Booking loaded:', data.booking);
      } else {
        throw new Error('Booking data not found in response');
      }
    } catch (error) {
      console.error('Error fetching booking:', error);
      setError(error.message || 'Failed to load booking');

      // If booking not found, redirect to bookings list
      if (error.message?.includes('not found') || error.status === 404) {
        toast.error('Booking not found');
        router.push('/admin/bookings');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleBookingUpdate = async (updatedBooking) => {
    try {
      setUpdating(true);

      // Update local state
      setBooking(updatedBooking);
      setShowEditModal(false);

      toast.success('Booking updated successfully');

      // Refresh booking data to ensure consistency
      await fetchBooking();
    } catch (error) {
      console.error('Error updating booking:', error);
      toast.error('Failed to update booking');
    } finally {
      setUpdating(false);
    }
  };

  const handleEdit = () => {
    setShowEditModal(true);
  };

  const handleCloseEdit = () => {
    setShowEditModal(false);
  };

  const handleBackToList = () => {
    router.push('/admin/bookings');
  };

  // Loading state
  if (loading) {
    return (
      <AdminLayout>
        <ProtectedRoute>
          <div className={styles.loadingContainer}>
            <div className={styles.loadingSpinner}></div>
            <p>Loading booking details...</p>
          </div>
        </ProtectedRoute>
      </AdminLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <AdminLayout>
        <ProtectedRoute>
          <div className={styles.errorContainer}>
            <div className={styles.errorIcon}>⚠️</div>
            <h2>Error Loading Booking</h2>
            <p>{error}</p>
            <div className={styles.errorActions}>
              <button
                onClick={fetchBooking}
                className={styles.retryButton}
              >
                Try Again
              </button>
              <button
                onClick={handleBackToList}
                className={styles.backButton}
              >
                Back to Bookings
              </button>
            </div>
          </div>
        </ProtectedRoute>
      </AdminLayout>
    );
  }

  // No booking found
  if (!booking) {
    return (
      <AdminLayout>
        <ProtectedRoute>
          <div className={styles.notFoundContainer}>
            <div className={styles.notFoundIcon}>📅</div>
            <h2>Booking Not Found</h2>
            <p>The booking you're looking for doesn't exist or has been deleted.</p>
            <button
              onClick={handleBackToList}
              className={styles.backButton}
            >
              Back to Bookings
            </button>
          </div>
        </ProtectedRoute>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <ProtectedRoute>
        <div className={styles.bookingDetailPage}>
          {/* Page Header */}
          <div className={styles.pageHeader}>
            <div className={styles.headerLeft}>
              <button
                onClick={handleBackToList}
                className={styles.backButton}
              >
                ← Back to Bookings
              </button>
              <div className={styles.pageTitle}>
                <h1>Booking Details</h1>
                <p className={styles.bookingId}>
                  ID: {booking.booking_reference || booking.id.slice(-8)}
                </p>
              </div>
            </div>

            <div className={styles.headerActions}>
              <button
                onClick={handleEdit}
                className={styles.editButton}
                disabled={updating}
              >
                {updating ? 'Updating...' : 'Edit Booking'}
              </button>
            </div>
          </div>

          {/* Booking Details */}
          <div className={styles.detailsContainer}>
            <EnhancedBookingDetails
              booking={booking}
              onEdit={handleEdit}
              onUpdate={handleBookingUpdate}
              onClose={handleBackToList}
            />
          </div>

          {/* Edit Modal */}
          {showEditModal && (
            <Modal
              isOpen={showEditModal}
              onClose={handleCloseEdit}
              title="Edit Booking"
              size="large"
            >
              <BookingForm
                booking={booking}
                onSave={handleBookingUpdate}
                onCancel={handleCloseEdit}
              />
            </Modal>
          )}
        </div>
      </ProtectedRoute>
    </AdminLayout>
  );
}

// Add authentication requirement
BookingDetailPage.requireAuth = true;
