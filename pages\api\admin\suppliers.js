import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * Admin Suppliers API Endpoint
 * Provides CRUD operations for supplier management
 */
export default async function handler(req, res) {
  // Authenticate request
  const { authorized, error, user } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  try {
    switch (req.method) {
      case 'GET':
        return await getSuppliers(req, res);
      case 'POST':
        return await createSupplier(req, res, user);
      case 'PUT':
        return await updateSupplier(req, res, user);
      case 'DELETE':
        return await deleteSupplier(req, res, user);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Suppliers API error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message
    });
  }
}

/**
 * Get suppliers with optional filtering and pagination
 */
async function getSuppliers(req, res) {
  const {
    search = '',
    active = 'all',
    sortBy = 'name',
    sortOrder = 'asc',
    limit = 100,
    offset = 0
  } = req.query;

  try {
    let query = supabaseAdmin
      .from('suppliers')
      .select('*', { count: 'exact' });

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,supplier_code.ilike.%${search}%,contact_email.ilike.%${search}%`);
    }

    // Apply active filter
    if (active !== 'all') {
      query = query.eq('is_active', active === 'true');
    }

    // Apply sorting
    const validSortColumns = ['name', 'supplier_code', 'rating', 'created_at', 'lead_time_days'];
    if (validSortColumns.includes(sortBy)) {
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
    } else {
      query = query.order('name', { ascending: true });
    }

    // Apply pagination
    const limitNum = Math.min(parseInt(limit) || 100, 200); // Max 200 items per request
    const offsetNum = parseInt(offset) || 0;
    query = query.range(offsetNum, offsetNum + limitNum - 1);

    const { data: suppliers, error, count } = await query;

    if (error) {
      throw error;
    }

    return res.status(200).json({
      suppliers: suppliers || [],
      pagination: {
        total: count || 0,
        offset: offsetNum,
        limit: limitNum,
        hasMore: (offsetNum + limitNum) < (count || 0)
      }
    });

  } catch (error) {
    console.error('Error fetching suppliers:', error);
    return res.status(500).json({
      error: 'Failed to fetch suppliers',
      message: error.message
    });
  }
}

/**
 * Create a new supplier
 */
async function createSupplier(req, res, user) {
  const {
    name,
    supplier_code,
    contact_person,
    email,
    phone,
    address,
    payment_terms,
    lead_time_days = 7,
    minimum_order_amount,
    currency = 'AUD',
    tax_id,
    website,
    contact_email,
    contact_phone,
    notes
  } = req.body;

  // Validate required fields
  if (!name) {
    return res.status(400).json({
      error: 'Validation error',
      message: 'Supplier name is required'
    });
  }

  try {
    const supplierData = {
      name: name.trim(),
      supplier_code: supplier_code?.trim() || null,
      contact_person: contact_person?.trim() || null,
      email: email?.trim() || null,
      phone: phone?.trim() || null,
      address: address?.trim() || null,
      payment_terms: payment_terms?.trim() || null,
      lead_time_days: parseInt(lead_time_days) || 7,
      minimum_order_amount: minimum_order_amount ? parseFloat(minimum_order_amount) : null,
      currency: currency || 'AUD',
      tax_id: tax_id?.trim() || null,
      website: website?.trim() || null,
      contact_email: contact_email?.trim() || null,
      contact_phone: contact_phone?.trim() || null,
      notes: notes?.trim() || null,
      is_active: true,
      created_by: user.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: supplier, error } = await supabaseAdmin
      .from('suppliers')
      .insert([supplierData])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return res.status(201).json({
      message: 'Supplier created successfully',
      supplier
    });

  } catch (error) {
    console.error('Error creating supplier:', error);
    
    // Handle unique constraint violations
    if (error.code === '23505') {
      return res.status(409).json({
        error: 'Supplier already exists',
        message: 'A supplier with this name or code already exists'
      });
    }

    return res.status(500).json({
      error: 'Failed to create supplier',
      message: error.message
    });
  }
}

/**
 * Update an existing supplier
 */
async function updateSupplier(req, res, user) {
  const { id } = req.query;
  const updateData = { ...req.body };

  if (!id) {
    return res.status(400).json({
      error: 'Validation error',
      message: 'Supplier ID is required'
    });
  }

  try {
    // Remove fields that shouldn't be updated directly
    delete updateData.id;
    delete updateData.created_at;
    delete updateData.created_by;

    // Add update metadata
    updateData.updated_at = new Date().toISOString();
    updateData.updated_by = user.id;

    // Clean string fields
    Object.keys(updateData).forEach(key => {
      if (typeof updateData[key] === 'string') {
        updateData[key] = updateData[key].trim() || null;
      }
    });

    const { data: supplier, error } = await supabaseAdmin
      .from('suppliers')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!supplier) {
      return res.status(404).json({
        error: 'Supplier not found',
        message: 'The specified supplier does not exist'
      });
    }

    return res.status(200).json({
      message: 'Supplier updated successfully',
      supplier
    });

  } catch (error) {
    console.error('Error updating supplier:', error);
    return res.status(500).json({
      error: 'Failed to update supplier',
      message: error.message
    });
  }
}

/**
 * Delete a supplier (soft delete by setting is_active to false)
 */
async function deleteSupplier(req, res, user) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({
      error: 'Validation error',
      message: 'Supplier ID is required'
    });
  }

  try {
    // Check if supplier has associated products
    const { data: products, error: productsError } = await supabaseAdmin
      .from('products')
      .select('id')
      .eq('supplier_id', id)
      .limit(1);

    if (productsError) {
      throw productsError;
    }

    if (products && products.length > 0) {
      return res.status(409).json({
        error: 'Cannot delete supplier',
        message: 'This supplier has associated products. Please reassign or remove the products first.'
      });
    }

    // Soft delete by setting is_active to false
    const { data: supplier, error } = await supabaseAdmin
      .from('suppliers')
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
        updated_by: user.id
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!supplier) {
      return res.status(404).json({
        error: 'Supplier not found',
        message: 'The specified supplier does not exist'
      });
    }

    return res.status(200).json({
      message: 'Supplier deactivated successfully',
      supplier
    });

  } catch (error) {
    console.error('Error deleting supplier:', error);
    return res.status(500).json({
      error: 'Failed to delete supplier',
      message: error.message
    });
  }
}
