import Head from 'next/head'
import Link from 'next/link'
import styles from '@/styles/admin/Login.module.css'

export default function ServiceKeyInstructions() {
  return (
    <>
      <Head>
        <title>Supabase Service Key Instructions | OceanSoulSparkles Admin</title>
        <meta name="description" content="Instructions for setting up the Supabase service role key" />
      </Head>

      <div className={styles.loginContainer}>
        <div className={styles.loginCard} style={{ maxWidth: '600px' }}>
          <div className={styles.logoContainer}>
            <img
              src="/images/bannerlogo.PNG"
              alt="OceanSoulSparkles Logo"
              className={styles.logo}
            />
          </div>

          <h1 className={styles.title}>Supabase Service Key Instructions</h1>

          <div style={{ textAlign: 'left', marginBottom: '20px' }}>
            <h2>How to Get Your Service Role Key</h2>

            <ol style={{ paddingLeft: '20px' }}>
              <li>
                <p><strong>Log in to Supabase:</strong></p>
                <p>Go to <a href="https://app.supabase.com" target="_blank" rel="noopener noreferrer">https://app.supabase.com</a> and log in to your account.</p>
              </li>

              <li>
                <p><strong>Select Your Project:</strong></p>
                <p>Click on the project that contains your OceanSoulSparkles database.</p>
              </li>

              <li>
                <p><strong>Go to Project Settings:</strong></p>
                <p>In the left sidebar, click on the gear icon (⚙️) or "Project Settings".</p>
              </li>

              <li>
                <p><strong>Find API Settings:</strong></p>
                <p>In the Project Settings menu, click on "API" in the sidebar.</p>
              </li>

              <li>
                <p><strong>Get the Service Role Key:</strong></p>
                <p>In the API Keys section, you'll see "service_role" key. Click the "Copy" button next to it.</p>
                <p><em>Note: This key has admin privileges, so keep it secure and never expose it in client-side code.</em></p>
              </li>

              <li>
                <p><strong>Update Your .env.local File:</strong></p>
                <p>Open your project's <code>.env.local</code> file and update the <code>SUPABASE_SERVICE_ROLE_KEY</code> value with the key you copied.</p>
                <pre style={{
                  backgroundColor: '#282c34',
                  color: '#abb2bf',
                  padding: '10px',
                  borderRadius: '5px',
                  overflowX: 'auto',
                  fontSize: '12px'
                }}>
{`# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
# Add the service role key for admin operations (server-side only)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here`}
                </pre>
              </li>

              <li>
                <p><strong>Restart Your Development Server:</strong></p>
                <p>After updating the .env.local file, restart your Next.js development server for the changes to take effect.</p>
              </li>
            </ol>

            <h2>Alternative: Set User Role Directly in Supabase</h2>

            <p>If you prefer to set the user role directly in the database:</p>

            <ol style={{ paddingLeft: '20px' }}>
              <li>
                <p><strong>Go to the SQL Editor:</strong></p>
                <p>In your Supabase dashboard, click on "SQL Editor" in the left sidebar.</p>
              </li>

              <li>
                <p><strong>Run the Following SQL:</strong></p>
                <pre style={{
                  backgroundColor: '#282c34',
                  color: '#abb2bf',
                  padding: '10px',
                  borderRadius: '5px',
                  overflowX: 'auto',
                  fontSize: '12px'
                }}>
{`-- Delete any existing role
DELETE FROM user_roles WHERE id = '8c59a3bc-a96b-4555-bdc4-6abe905ae761';

-- Insert new role
INSERT INTO user_roles (id, role)
VALUES ('8c59a3bc-a96b-4555-bdc4-6abe905ae761', 'admin');

-- Verify
SELECT * FROM user_roles WHERE id = '8c59a3bc-a96b-4555-bdc4-6abe905ae761';`}
                </pre>
              </li>
            </ol>
          </div>

          <div className={styles.backToSite}>
            <Link href="/admin/login" className={styles.link}>
              ← Back to login
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}
