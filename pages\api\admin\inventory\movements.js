import { supabaseAdmin } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * API endpoint for inventory stock movements
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Authenticate request
  const { authorized, error, user } = await authenticateAdminRequest(req);
  if (!authorized) {
    return res.status(401).json({
      error: 'Unauthorized access',
      message: error?.message || 'Authentication failed'
    });
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getMovements(req, res);
    case 'POST':
      return createMovement(req, res);
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

/**
 * Get stock movements with optional filters
 */
async function getMovements(req, res) {
  try {
    const {
      product_id,
      type,
      date_from,
      date_to,
      search,
      page = 1,
      limit = 20,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = req.query;

    // Calculate pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    // Start building the query
    let query = supabaseAdmin
      .from('inventory_transactions')
      .select(`
        id,
        product_id,
        products:product_id (name),
        quantity,
        transaction_type,
        notes,
        created_at,
        created_by,
        user_profiles!inventory_transactions_created_by_fkey (email, display_name)
      `, { count: 'exact' });

    // Apply filters
    if (product_id) {
      query = query.eq('product_id', product_id);
    }

    if (type) {
      query = query.eq('transaction_type', type);
    }

    if (date_from) {
      query = query.gte('created_at', new Date(date_from).toISOString());
    }

    if (date_to) {
      // Add one day to include the end date
      const endDate = new Date(date_to);
      endDate.setDate(endDate.getDate() + 1);
      query = query.lt('created_at', endDate.toISOString());
    }

    if (search) {
      // Join with products table to search by product name
      query = query.or(`products.name.ilike.%${search}%`);
    }

    // Apply sorting
    const validSortFields = ['created_at', 'transaction_type', 'quantity'];
    const sortField = validSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order === 'asc' ? 'asc' : 'desc';

    query = query.order(sortField, { ascending: sortDirection === 'asc' });

    // Apply pagination
    query = query.range(from, to);

    // Execute query
    const { data, error: queryError, count } = await query;

    if (queryError) {
      throw queryError;
    }

    // Format movements data
    const formattedMovements = data.map(movement => ({
      id: movement.id,
      product_id: movement.product_id,
      product_name: movement.products?.name || 'Unknown Product',
      quantity: movement.quantity,
      type: movement.transaction_type,
      notes: movement.notes,
      created_at: movement.created_at,
      user_name: movement.user_profiles?.display_name || movement.user_profiles?.email || 'Unknown User'
    }));

    // Return movements
    return res.status(200).json({
      movements: formattedMovements,
      count: count || 0,
      page: parseInt(page),
      limit: parseInt(limit),
      total_pages: Math.ceil((count || 0) / limit)
    });
  } catch (err) {
    console.error('Error fetching stock movements:', err);
    return res.status(500).json({ error: 'Failed to fetch stock movements' });
  }
}

/**
 * Create a new stock movement
 */
async function createMovement(req, res) {
  try {
    const { product_id, quantity, transaction_type, notes } = req.body;

    // Validate required fields
    if (!product_id || quantity === undefined || !transaction_type) {
      return res.status(400).json({ error: 'Product ID, quantity, and transaction type are required' });
    }

    // Validate transaction type
    const validTypes = ['restock', 'adjustment', 'sale', 'return', 'damaged', 'lost'];
    if (!validTypes.includes(transaction_type)) {
      return res.status(400).json({ error: 'Invalid transaction type' });
    }

    // Start a transaction
    const { data: product, error: productError } = await supabaseAdmin
      .from('products')
      .select('id, stock')
      .eq('id', product_id)
      .single();

    if (productError) {
      throw productError;
    }

    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Calculate new stock level
    const currentStock = product.stock || 0;
    const newStock = currentStock + quantity;

    // Prevent negative stock for certain transaction types
    if (newStock < 0 && ['sale', 'damaged', 'lost'].includes(transaction_type)) {
      return res.status(400).json({
        error: 'Insufficient stock',
        current_stock: currentStock
      });
    }

    // Update product stock
    const { error: updateError } = await supabaseAdmin
      .from('products')
      .update({ stock: newStock })
      .eq('id', product_id);

    if (updateError) {
      throw updateError;
    }

    // Create inventory transaction record
    const { data: movement, error: movementError } = await supabaseAdmin
      .from('inventory_transactions')
      .insert([
        {
          product_id,
          quantity,
          transaction_type,
          notes,
          created_by: user.id
        }
      ])
      .select();

    if (movementError) {
      throw movementError;
    }

    // Return success response
    return res.status(201).json({
      success: true,
      movement: movement[0],
      new_stock: newStock
    });
  } catch (err) {
    console.error('Error creating stock movement:', err);
    return res.status(500).json({ error: 'Failed to create stock movement' });
  }
}
