-- Inventory Management System Enhancements Migration
-- This script adds new tables and columns to support enhanced inventory management

-- =============================================
-- ENHANCED PRODUCTS TABLE
-- =============================================

-- Add new columns to existing products table
ALTER TABLE public.products
ADD COLUMN IF NOT EXISTS barcode TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS weight DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS dimensions JSONB, -- {length, width, height, unit}
ADD COLUMN IF NOT EXISTS reorder_point INTEGER,
ADD COLUMN IF NOT EXISTS reorder_quantity INTEGER,
ADD COLUMN IF NOT EXISTS max_stock_level INTEGER,
ADD COLUMN IF NOT EXISTS abc_classification TEXT CHECK (abc_classification IN ('A', 'B', 'C')),
ADD COLUMN IF NOT EXISTS seasonal_item BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS perishable BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS shelf_life_days INTEGER,
ADD COLUMN IF NOT EXISTS supplier_id UUID REFERENCES public.suppliers(id),
ADD COLUMN IF NOT EXISTS lead_time_days INTEGER DEFAULT 7,
ADD COLUMN IF NOT EXISTS minimum_order_quantity INTEGER DEFAULT 1;

-- Create indexes for improved performance
CREATE INDEX IF NOT EXISTS products_barcode_idx ON public.products(barcode);
CREATE INDEX IF NOT EXISTS products_abc_classification_idx ON public.products(abc_classification);
CREATE INDEX IF NOT EXISTS products_supplier_idx ON public.products(supplier_id);
CREATE INDEX IF NOT EXISTS products_reorder_point_idx ON public.products(reorder_point);

-- Enhanced full-text search index
CREATE INDEX IF NOT EXISTS products_enhanced_search_idx ON public.products
USING gin(to_tsvector('english',
  name || ' ' ||
  COALESCE(description, '') || ' ' ||
  COALESCE(sku, '') || ' ' ||
  COALESCE(barcode, '')
));

-- =============================================
-- ENHANCED INVENTORY TABLE
-- =============================================

-- Add new columns to existing inventory table
ALTER TABLE public.inventory
ADD COLUMN IF NOT EXISTS reserved_quantity INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS available_quantity INTEGER GENERATED ALWAYS AS (quantity - reserved_quantity) STORED,
ADD COLUMN IF NOT EXISTS last_count_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS cycle_count_frequency INTEGER DEFAULT 90, -- days
ADD COLUMN IF NOT EXISTS variance_threshold DECIMAL(5,2) DEFAULT 5.0,
ADD COLUMN IF NOT EXISTS location_id UUID,
ADD COLUMN IF NOT EXISTS bin_location TEXT;

-- Create indexes
CREATE INDEX IF NOT EXISTS inventory_available_quantity_idx ON public.inventory(available_quantity);
CREATE INDEX IF NOT EXISTS inventory_last_count_idx ON public.inventory(last_count_date);
CREATE INDEX IF NOT EXISTS inventory_location_idx ON public.inventory(location_id);

-- =============================================
-- ENHANCED SUPPLIERS TABLE
-- =============================================

-- Add new columns to existing suppliers table
ALTER TABLE public.suppliers
ADD COLUMN IF NOT EXISTS supplier_code TEXT UNIQUE,
ADD COLUMN IF NOT EXISTS payment_terms TEXT,
ADD COLUMN IF NOT EXISTS lead_time_days INTEGER DEFAULT 7,
ADD COLUMN IF NOT EXISTS minimum_order_amount DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'AUD',
ADD COLUMN IF NOT EXISTS tax_id TEXT,
ADD COLUMN IF NOT EXISTS rating DECIMAL(3,2) CHECK (rating >= 0 AND rating <= 5),
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS website TEXT,
ADD COLUMN IF NOT EXISTS contact_email TEXT,
ADD COLUMN IF NOT EXISTS contact_phone TEXT;

-- Create indexes
CREATE INDEX IF NOT EXISTS suppliers_code_idx ON public.suppliers(supplier_code);
CREATE INDEX IF NOT EXISTS suppliers_rating_idx ON public.suppliers(rating);
CREATE INDEX IF NOT EXISTS suppliers_active_idx ON public.suppliers(is_active);

-- =============================================
-- REORDER RULES TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.reorder_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
  reorder_point INTEGER NOT NULL,
  reorder_quantity INTEGER NOT NULL,
  max_stock_level INTEGER,
  lead_time_days INTEGER DEFAULT 7,
  safety_stock INTEGER DEFAULT 0,
  economic_order_quantity INTEGER,
  is_active BOOLEAN DEFAULT TRUE,
  auto_generate_po BOOLEAN DEFAULT FALSE,
  preferred_supplier_id UUID REFERENCES public.suppliers(id),
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(product_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS reorder_rules_product_idx ON public.reorder_rules(product_id);
CREATE INDEX IF NOT EXISTS reorder_rules_active_idx ON public.reorder_rules(is_active);
CREATE INDEX IF NOT EXISTS reorder_rules_supplier_idx ON public.reorder_rules(preferred_supplier_id);

-- =============================================
-- SUPPLIER PERFORMANCE TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.supplier_performance (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  supplier_id UUID REFERENCES public.suppliers(id) ON DELETE CASCADE,
  average_lead_time DECIMAL(5,2),
  on_time_delivery_rate DECIMAL(5,2) CHECK (on_time_delivery_rate >= 0 AND on_time_delivery_rate <= 100),
  quality_rating DECIMAL(3,2) CHECK (quality_rating >= 0 AND quality_rating <= 5),
  cost_competitiveness DECIMAL(3,2) CHECK (cost_competitiveness >= 0 AND cost_competitiveness <= 5),
  communication_rating DECIMAL(3,2) CHECK (communication_rating >= 0 AND communication_rating <= 5),
  total_orders INTEGER DEFAULT 0,
  total_value DECIMAL(12,2) DEFAULT 0,
  last_order_date TIMESTAMPTZ,
  last_calculated TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(supplier_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS supplier_performance_supplier_idx ON public.supplier_performance(supplier_id);
CREATE INDEX IF NOT EXISTS supplier_performance_rating_idx ON public.supplier_performance(quality_rating);

-- =============================================
-- PRODUCT LOCATIONS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.product_locations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
  location_name TEXT NOT NULL,
  warehouse_code TEXT,
  aisle TEXT,
  shelf TEXT,
  bin_location TEXT,
  quantity INTEGER DEFAULT 0,
  reserved_quantity INTEGER DEFAULT 0,
  available_quantity INTEGER GENERATED ALWAYS AS (quantity - reserved_quantity) STORED,
  is_primary_location BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS product_locations_product_idx ON public.product_locations(product_id);
CREATE INDEX IF NOT EXISTS product_locations_location_idx ON public.product_locations(location_name);
CREATE INDEX IF NOT EXISTS product_locations_primary_idx ON public.product_locations(is_primary_location);

-- =============================================
-- INVENTORY VALUATIONS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.inventory_valuations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
  valuation_method TEXT NOT NULL CHECK (valuation_method IN ('fifo', 'lifo', 'average_cost', 'standard_cost')),
  unit_cost DECIMAL(10,4) NOT NULL,
  quantity INTEGER NOT NULL,
  total_value DECIMAL(12,2) NOT NULL,
  valuation_date DATE NOT NULL,
  batch_number TEXT,
  expiry_date DATE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS inventory_valuations_product_idx ON public.inventory_valuations(product_id);
CREATE INDEX IF NOT EXISTS inventory_valuations_method_idx ON public.inventory_valuations(valuation_method);
CREATE INDEX IF NOT EXISTS inventory_valuations_date_idx ON public.inventory_valuations(valuation_date);

-- =============================================
-- DEMAND FORECASTS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.demand_forecasts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
  forecast_period TEXT NOT NULL CHECK (forecast_period IN ('daily', 'weekly', 'monthly', 'quarterly')),
  forecast_date DATE NOT NULL,
  predicted_demand INTEGER NOT NULL,
  confidence_level DECIMAL(5,2) CHECK (confidence_level >= 0 AND confidence_level <= 100),
  actual_demand INTEGER,
  forecast_accuracy DECIMAL(5,2),
  seasonal_factor DECIMAL(5,2) DEFAULT 1.0,
  trend_factor DECIMAL(5,2) DEFAULT 1.0,
  model_used TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS demand_forecasts_product_idx ON public.demand_forecasts(product_id);
CREATE INDEX IF NOT EXISTS demand_forecasts_date_idx ON public.demand_forecasts(forecast_date);
CREATE INDEX IF NOT EXISTS demand_forecasts_period_idx ON public.demand_forecasts(forecast_period);

-- =============================================
-- PRODUCT COSTS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.product_costs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
  cost_type TEXT NOT NULL CHECK (cost_type IN ('purchase', 'landed', 'overhead', 'labor', 'shipping', 'handling', 'customs')),
  cost_amount DECIMAL(10,4) NOT NULL,
  cost_date DATE NOT NULL,
  supplier_id UUID REFERENCES public.suppliers(id),
  purchase_order_id UUID REFERENCES public.purchase_orders(id),
  currency TEXT DEFAULT 'AUD',
  exchange_rate DECIMAL(10,6) DEFAULT 1.0,
  notes TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS product_costs_product_idx ON public.product_costs(product_id);
CREATE INDEX IF NOT EXISTS product_costs_type_idx ON public.product_costs(cost_type);
CREATE INDEX IF NOT EXISTS product_costs_date_idx ON public.product_costs(cost_date);
CREATE INDEX IF NOT EXISTS product_costs_supplier_idx ON public.product_costs(supplier_id);

-- =============================================
-- INVENTORY ALERTS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.inventory_alerts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID REFERENCES public.products(id) ON DELETE CASCADE,
  alert_type TEXT NOT NULL CHECK (alert_type IN ('low_stock', 'out_of_stock', 'overstock', 'reorder_needed', 'expiry_warning', 'slow_moving')),
  severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  threshold_value INTEGER,
  current_value INTEGER,
  is_resolved BOOLEAN DEFAULT FALSE,
  resolved_by UUID REFERENCES auth.users(id),
  resolved_at TIMESTAMPTZ,
  resolution_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS inventory_alerts_product_idx ON public.inventory_alerts(product_id);
CREATE INDEX IF NOT EXISTS inventory_alerts_type_idx ON public.inventory_alerts(alert_type);
CREATE INDEX IF NOT EXISTS inventory_alerts_severity_idx ON public.inventory_alerts(severity);
CREATE INDEX IF NOT EXISTS inventory_alerts_resolved_idx ON public.inventory_alerts(is_resolved);

-- =============================================
-- ENHANCED INVENTORY TRANSACTIONS TABLE
-- =============================================

-- Add new columns to existing inventory_transactions table
ALTER TABLE public.inventory_transactions
ADD COLUMN IF NOT EXISTS unit_cost DECIMAL(10,4),
ADD COLUMN IF NOT EXISTS total_cost DECIMAL(12,2),
ADD COLUMN IF NOT EXISTS supplier_id UUID REFERENCES public.suppliers(id),
ADD COLUMN IF NOT EXISTS location_id UUID,
ADD COLUMN IF NOT EXISTS batch_number TEXT,
ADD COLUMN IF NOT EXISTS expiry_date DATE,
ADD COLUMN IF NOT EXISTS reason_code TEXT,
ADD COLUMN IF NOT EXISTS approval_required BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMPTZ;

-- Create additional indexes
CREATE INDEX IF NOT EXISTS inventory_transactions_cost_idx ON public.inventory_transactions(unit_cost, total_cost);
CREATE INDEX IF NOT EXISTS inventory_transactions_supplier_idx ON public.inventory_transactions(supplier_id);
CREATE INDEX IF NOT EXISTS inventory_transactions_batch_idx ON public.inventory_transactions(batch_number);
CREATE INDEX IF NOT EXISTS inventory_transactions_approval_idx ON public.inventory_transactions(approval_required, approved_by);

-- =============================================
-- INVENTORY ANALYTICS VIEWS
-- =============================================

-- Inventory summary view
CREATE OR REPLACE VIEW public.inventory_summary AS
SELECT
  p.id,
  p.name,
  p.sku,
  p.barcode,
  p.category,
  p.price,
  p.cost_price,
  p.abc_classification,
  i.quantity,
  i.reserved_quantity,
  i.available_quantity,
  i.low_stock_threshold,
  p.reorder_point,
  p.reorder_quantity,
  s.name as supplier_name,
  s.lead_time_days,
  CASE
    WHEN i.quantity <= 0 THEN 'out_of_stock'
    WHEN i.quantity <= i.low_stock_threshold THEN 'low_stock'
    WHEN p.max_stock_level IS NOT NULL AND i.quantity > p.max_stock_level THEN 'overstocked'
    ELSE 'in_stock'
  END as stock_status,
  (i.quantity * COALESCE(p.cost_price, p.price * 0.6)) as inventory_value,
  i.last_restock_date,
  i.last_count_date
FROM public.products p
LEFT JOIN public.inventory i ON p.id = i.product_id
LEFT JOIN public.suppliers s ON p.supplier_id = s.id;

-- ABC analysis view
CREATE OR REPLACE VIEW public.abc_analysis AS
WITH product_sales AS (
  SELECT
    p.id,
    p.name,
    SUM(oi.quantity * oi.price) as annual_revenue,
    SUM(oi.quantity) as annual_quantity
  FROM public.products p
  LEFT JOIN public.order_items oi ON p.id = oi.product_id
  LEFT JOIN public.orders o ON oi.order_id = o.id
  WHERE o.created_at >= NOW() - INTERVAL '365 days'
  GROUP BY p.id, p.name
),
revenue_ranking AS (
  SELECT
    *,
    PERCENT_RANK() OVER (ORDER BY annual_revenue DESC) as revenue_percentile
  FROM product_sales
)
SELECT
  *,
  CASE
    WHEN revenue_percentile <= 0.2 THEN 'A'
    WHEN revenue_percentile <= 0.5 THEN 'B'
    ELSE 'C'
  END as suggested_abc_class
FROM revenue_ranking;

-- Inventory turnover view
CREATE OR REPLACE VIEW public.inventory_turnover AS
SELECT
  p.id,
  p.name,
  p.sku,
  COALESCE(SUM(oi.quantity), 0) as units_sold_annual,
  COALESCE(AVG(i.quantity), 0) as average_inventory,
  CASE
    WHEN AVG(i.quantity) > 0 THEN COALESCE(SUM(oi.quantity), 0) / AVG(i.quantity)
    ELSE 0
  END as turnover_ratio,
  CASE
    WHEN COALESCE(SUM(oi.quantity), 0) > 0 THEN 365.0 / (COALESCE(SUM(oi.quantity), 0) / AVG(i.quantity))
    ELSE NULL
  END as days_of_inventory
FROM public.products p
LEFT JOIN public.inventory i ON p.id = i.product_id
LEFT JOIN public.order_items oi ON p.id = oi.product_id
LEFT JOIN public.orders o ON oi.order_id = o.id
WHERE o.created_at >= NOW() - INTERVAL '365 days' OR o.created_at IS NULL
GROUP BY p.id, p.name, p.sku;

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to calculate ABC classification
CREATE OR REPLACE FUNCTION public.calculate_abc_classification(product_id UUID)
RETURNS TEXT AS $$
DECLARE
  revenue_percentile DECIMAL;
BEGIN
  SELECT
    PERCENT_RANK() OVER (ORDER BY annual_revenue DESC)
  INTO revenue_percentile
  FROM (
    SELECT
      p.id,
      SUM(oi.quantity * oi.price) as annual_revenue
    FROM public.products p
    LEFT JOIN public.order_items oi ON p.id = oi.product_id
    LEFT JOIN public.orders o ON oi.order_id = o.id
    WHERE o.created_at >= NOW() - INTERVAL '365 days'
      AND p.id = product_id
    GROUP BY p.id
  ) revenue_data;

  IF revenue_percentile IS NULL THEN
    RETURN 'C';
  ELSIF revenue_percentile <= 0.2 THEN
    RETURN 'A';
  ELSIF revenue_percentile <= 0.5 THEN
    RETURN 'B';
  ELSE
    RETURN 'C';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to check reorder requirements
CREATE OR REPLACE FUNCTION public.check_reorder_requirements()
RETURNS TABLE(
  product_id UUID,
  product_name TEXT,
  current_stock INTEGER,
  reorder_point INTEGER,
  reorder_quantity INTEGER,
  supplier_id UUID,
  supplier_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.id,
    p.name,
    i.quantity,
    rr.reorder_point,
    rr.reorder_quantity,
    rr.preferred_supplier_id,
    s.name
  FROM public.products p
  JOIN public.inventory i ON p.id = i.product_id
  JOIN public.reorder_rules rr ON p.id = rr.product_id
  LEFT JOIN public.suppliers s ON rr.preferred_supplier_id = s.id
  WHERE rr.is_active = TRUE
    AND i.quantity <= rr.reorder_point;
END;
$$ LANGUAGE plpgsql;

-- Function to generate inventory alerts
CREATE OR REPLACE FUNCTION public.generate_inventory_alerts()
RETURNS INTEGER AS $$
DECLARE
  alert_count INTEGER := 0;
  product_record RECORD;
BEGIN
  -- Clear existing unresolved alerts
  DELETE FROM public.inventory_alerts WHERE is_resolved = FALSE;

  -- Generate low stock alerts
  FOR product_record IN
    SELECT p.id, p.name, i.quantity, i.low_stock_threshold
    FROM public.products p
    JOIN public.inventory i ON p.id = i.product_id
    WHERE i.quantity <= i.low_stock_threshold AND i.quantity > 0
  LOOP
    INSERT INTO public.inventory_alerts (
      product_id, alert_type, severity, title, message,
      threshold_value, current_value
    ) VALUES (
      product_record.id,
      'low_stock',
      CASE WHEN product_record.quantity <= product_record.low_stock_threshold * 0.5 THEN 'high' ELSE 'medium' END,
      'Low Stock Alert',
      'Product "' || product_record.name || '" is running low on stock',
      product_record.low_stock_threshold,
      product_record.quantity
    );
    alert_count := alert_count + 1;
  END LOOP;

  -- Generate out of stock alerts
  FOR product_record IN
    SELECT p.id, p.name, i.quantity
    FROM public.products p
    JOIN public.inventory i ON p.id = i.product_id
    WHERE i.quantity <= 0
  LOOP
    INSERT INTO public.inventory_alerts (
      product_id, alert_type, severity, title, message,
      threshold_value, current_value
    ) VALUES (
      product_record.id,
      'out_of_stock',
      'critical',
      'Out of Stock Alert',
      'Product "' || product_record.name || '" is out of stock',
      0,
      product_record.quantity
    );
    alert_count := alert_count + 1;
  END LOOP;

  RETURN alert_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update inventory after transaction
CREATE OR REPLACE FUNCTION public.update_inventory_after_transaction()
RETURNS TRIGGER AS $$
BEGIN
  -- Update inventory quantity based on transaction
  IF NEW.transaction_type = 'in' THEN
    UPDATE public.inventory
    SET quantity = quantity + NEW.quantity,
        last_restock_date = CASE WHEN NEW.quantity > 0 THEN NOW() ELSE last_restock_date END
    WHERE product_id = NEW.product_id;
  ELSIF NEW.transaction_type = 'out' THEN
    UPDATE public.inventory
    SET quantity = quantity - NEW.quantity
    WHERE product_id = NEW.product_id;
  ELSIF NEW.transaction_type = 'adjustment' THEN
    UPDATE public.inventory
    SET quantity = NEW.quantity
    WHERE product_id = NEW.product_id;
  END IF;

  -- Check for reorder requirements
  PERFORM public.generate_inventory_alerts();

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update inventory after transaction
DROP TRIGGER IF EXISTS update_inventory_trigger ON public.inventory_transactions;
CREATE TRIGGER update_inventory_trigger
  AFTER INSERT ON public.inventory_transactions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_inventory_after_transaction();

-- Function to update ABC classification periodically
CREATE OR REPLACE FUNCTION public.update_abc_classifications()
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER := 0;
  product_record RECORD;
BEGIN
  FOR product_record IN
    SELECT id FROM public.products
  LOOP
    UPDATE public.products
    SET abc_classification = public.calculate_abc_classification(product_record.id)
    WHERE id = product_record.id;
    updated_count := updated_count + 1;
  END LOOP;

  RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on new tables
ALTER TABLE public.reorder_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.supplier_performance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory_valuations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.demand_forecasts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_costs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory_alerts ENABLE ROW LEVEL SECURITY;

-- Policies for reorder_rules
CREATE POLICY "Staff can manage reorder rules" ON public.reorder_rules
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'staff')
    )
  );

-- Policies for supplier_performance
CREATE POLICY "Staff can view supplier performance" ON public.supplier_performance
  FOR SELECT USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'staff')
    )
  );

-- Policies for product_locations
CREATE POLICY "Staff can manage product locations" ON public.product_locations
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'staff')
    )
  );

-- Policies for inventory_valuations
CREATE POLICY "Staff can manage inventory valuations" ON public.inventory_valuations
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'staff')
    )
  );

-- Policies for demand_forecasts
CREATE POLICY "Staff can manage demand forecasts" ON public.demand_forecasts
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'staff')
    )
  );

-- Policies for product_costs
CREATE POLICY "Staff can manage product costs" ON public.product_costs
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'staff')
    )
  );

-- Policies for inventory_alerts
CREATE POLICY "Staff can manage inventory alerts" ON public.inventory_alerts
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.id = auth.uid() AND profiles.role IN ('admin', 'staff')
    )
  );

-- =============================================
-- PERFORMANCE OPTIMIZATIONS
-- =============================================

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS products_inventory_composite_idx
ON public.products(category, abc_classification, supplier_id);

CREATE INDEX IF NOT EXISTS inventory_stock_status_idx
ON public.inventory(quantity, low_stock_threshold, last_restock_date);

CREATE INDEX IF NOT EXISTS inventory_transactions_analytics_idx
ON public.inventory_transactions(product_id, transaction_type, created_at, quantity);

-- Analyze tables for query optimization
ANALYZE public.products;
ANALYZE public.inventory;
ANALYZE public.suppliers;
ANALYZE public.reorder_rules;
ANALYZE public.supplier_performance;
ANALYZE public.product_locations;
ANALYZE public.inventory_valuations;
ANALYZE public.demand_forecasts;
ANALYZE public.product_costs;
ANALYZE public.inventory_alerts;
ANALYZE public.inventory_transactions;
