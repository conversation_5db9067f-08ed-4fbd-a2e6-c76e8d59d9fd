import { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import styles from '@/styles/admin/AdvancedInventorySearch.module.css';

export default function AdvancedInventorySearch({ onFiltersChange, onProductsUpdate }) {
  const [filters, setFilters] = useState({
    search: '',
    category: 'all',
    stockLevel: 'all',
    priceRange: { min: '', max: '' },
    costRange: { min: '', max: '' },
    supplier: 'all',
    lastRestocked: '',
    abcClassification: 'all',
    location: 'all'
  });

  const [savedFilters, setSavedFilters] = useState([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [categories, setCategories] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(false);

  const stockLevelOptions = [
    { value: 'all', label: 'All Stock Levels' },
    { value: 'in_stock', label: 'In Stock' },
    { value: 'low_stock', label: 'Low Stock' },
    { value: 'out_of_stock', label: 'Out of Stock' },
    { value: 'overstocked', label: 'Overstocked' }
  ];

  const abcClassificationOptions = [
    { value: 'all', label: 'All Classifications' },
    { value: 'A', label: 'Class A (High Value)' },
    { value: 'B', label: 'Class B (Medium Value)' },
    { value: 'C', label: 'Class C (Low Value)' }
  ];

  const lastRestockedOptions = [
    { value: '', label: 'Any time' },
    { value: '7', label: 'Last 7 days' },
    { value: '30', label: 'Last 30 days' },
    { value: '90', label: 'Last 90 days' },
    { value: '365', label: 'Last year' }
  ];

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchFilters) => {
      setLoading(true);
      try {
        const queryParams = new URLSearchParams();
        
        Object.entries(searchFilters).forEach(([key, value]) => {
          if (value && value !== 'all' && value !== '') {
            if (typeof value === 'object') {
              queryParams.append(key, JSON.stringify(value));
            } else {
              queryParams.append(key, value);
            }
          }
        });

        const response = await fetch(`/api/admin/inventory/search?${queryParams}`);
        const data = await response.json();

        if (response.ok) {
          onProductsUpdate && onProductsUpdate(data);
        } else {
          console.error('Search error:', data.error);
        }
      } catch (error) {
        console.error('Search request failed:', error);
      } finally {
        setLoading(false);
      }
    }, 300),
    [onProductsUpdate]
  );

  useEffect(() => {
    debouncedSearch(filters);
    onFiltersChange && onFiltersChange(filters);
  }, [filters, debouncedSearch, onFiltersChange]);

  useEffect(() => {
    fetchCategories();
    fetchSuppliers();
    loadSavedFilters();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/inventory/categories');
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchSuppliers = async () => {
    try {
      const response = await fetch('/api/admin/suppliers');
      const data = await response.json();
      setSuppliers(data.suppliers || []);
    } catch (error) {
      console.error('Error fetching suppliers:', error);
    }
  };

  const loadSavedFilters = () => {
    const saved = localStorage.getItem('inventoryFilters');
    if (saved) {
      setSavedFilters(JSON.parse(saved));
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleNestedFilterChange = (parentKey, childKey, value) => {
    setFilters(prev => ({
      ...prev,
      [parentKey]: {
        ...prev[parentKey],
        [childKey]: value
      }
    }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      category: 'all',
      stockLevel: 'all',
      priceRange: { min: '', max: '' },
      costRange: { min: '', max: '' },
      supplier: 'all',
      lastRestocked: '',
      abcClassification: 'all',
      location: 'all'
    });
  };

  const saveCurrentFilters = () => {
    const filterName = prompt('Enter a name for this filter set:');
    if (filterName) {
      const newSavedFilter = {
        id: Date.now(),
        name: filterName,
        filters: { ...filters }
      };
      const updatedFilters = [...savedFilters, newSavedFilter];
      setSavedFilters(updatedFilters);
      localStorage.setItem('inventoryFilters', JSON.stringify(updatedFilters));
    }
  };

  const loadSavedFilter = (savedFilter) => {
    setFilters(savedFilter.filters);
  };

  const deleteSavedFilter = (filterId) => {
    const updatedFilters = savedFilters.filter(f => f.id !== filterId);
    setSavedFilters(updatedFilters);
    localStorage.setItem('inventoryFilters', JSON.stringify(updatedFilters));
  };

  const hasActiveFilters = () => {
    return filters.search ||
           filters.category !== 'all' ||
           filters.stockLevel !== 'all' ||
           filters.priceRange.min ||
           filters.priceRange.max ||
           filters.costRange.min ||
           filters.costRange.max ||
           filters.supplier !== 'all' ||
           filters.lastRestocked ||
           filters.abcClassification !== 'all';
  };

  return (
    <div className={styles.searchContainer}>
      {/* Quick Search Bar */}
      <div className={styles.quickSearch}>
        <div className={styles.searchInputGroup}>
          <input
            type="text"
            placeholder="Search products by name, SKU, barcode..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className={styles.searchInput}
          />
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`${styles.advancedToggle} ${showAdvanced ? styles.active : ''}`}
          >
            Advanced {showAdvanced ? '▲' : '▼'}
          </button>
          {loading && <div className={styles.loadingIndicator}>Searching...</div>}
        </div>

        <div className={styles.quickFilters}>
          <select
            value={filters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className={styles.quickSelect}
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>

          <select
            value={filters.stockLevel}
            onChange={(e) => handleFilterChange('stockLevel', e.target.value)}
            className={styles.quickSelect}
          >
            {stockLevelOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className={styles.advancedFilters}>
          <div className={styles.filterRow}>
            <div className={styles.filterSection}>
              <label>Price Range</label>
              <div className={styles.rangeInputs}>
                <input
                  type="number"
                  placeholder="Min price"
                  value={filters.priceRange.min}
                  onChange={(e) => handleNestedFilterChange('priceRange', 'min', e.target.value)}
                  className={styles.rangeInput}
                />
                <span>to</span>
                <input
                  type="number"
                  placeholder="Max price"
                  value={filters.priceRange.max}
                  onChange={(e) => handleNestedFilterChange('priceRange', 'max', e.target.value)}
                  className={styles.rangeInput}
                />
              </div>
            </div>

            <div className={styles.filterSection}>
              <label>Cost Range</label>
              <div className={styles.rangeInputs}>
                <input
                  type="number"
                  placeholder="Min cost"
                  value={filters.costRange.min}
                  onChange={(e) => handleNestedFilterChange('costRange', 'min', e.target.value)}
                  className={styles.rangeInput}
                />
                <span>to</span>
                <input
                  type="number"
                  placeholder="Max cost"
                  value={filters.costRange.max}
                  onChange={(e) => handleNestedFilterChange('costRange', 'max', e.target.value)}
                  className={styles.rangeInput}
                />
              </div>
            </div>
          </div>

          <div className={styles.filterRow}>
            <div className={styles.filterSection}>
              <label>Supplier</label>
              <select
                value={filters.supplier}
                onChange={(e) => handleFilterChange('supplier', e.target.value)}
                className={styles.select}
              >
                <option value="all">All Suppliers</option>
                {suppliers.map(supplier => (
                  <option key={supplier.id} value={supplier.name}>
                    {supplier.name}
                  </option>
                ))}
              </select>
            </div>

            <div className={styles.filterSection}>
              <label>Last Restocked</label>
              <select
                value={filters.lastRestocked}
                onChange={(e) => handleFilterChange('lastRestocked', e.target.value)}
                className={styles.select}
              >
                {lastRestockedOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className={styles.filterSection}>
              <label>ABC Classification</label>
              <select
                value={filters.abcClassification}
                onChange={(e) => handleFilterChange('abcClassification', e.target.value)}
                className={styles.select}
              >
                {abcClassificationOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className={styles.filterActions}>
            <button onClick={clearFilters} className={styles.clearButton}>
              Clear All Filters
            </button>
            <button onClick={saveCurrentFilters} className={styles.saveButton}>
              Save Filter Set
            </button>
          </div>
        </div>
      )}

      {/* Saved Filters */}
      {savedFilters.length > 0 && (
        <div className={styles.savedFilters}>
          <h4>Saved Filters:</h4>
          <div className={styles.savedFiltersList}>
            {savedFilters.map(savedFilter => (
              <div key={savedFilter.id} className={styles.savedFilterItem}>
                <button
                  onClick={() => loadSavedFilter(savedFilter)}
                  className={styles.savedFilterButton}
                >
                  {savedFilter.name}
                </button>
                <button
                  onClick={() => deleteSavedFilter(savedFilter.id)}
                  className={styles.deleteSavedFilter}
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters() && (
        <div className={styles.activeFilters}>
          <span className={styles.activeFiltersLabel}>Active Filters:</span>
          <div className={styles.activeFilterTags}>
            {filters.search && (
              <span className={styles.filterTag}>
                Search: "{filters.search}"
                <button onClick={() => handleFilterChange('search', '')}>×</button>
              </span>
            )}
            {filters.category !== 'all' && (
              <span className={styles.filterTag}>
                Category: {categories.find(c => c.id === filters.category)?.name}
                <button onClick={() => handleFilterChange('category', 'all')}>×</button>
              </span>
            )}
            {filters.stockLevel !== 'all' && (
              <span className={styles.filterTag}>
                Stock: {stockLevelOptions.find(s => s.value === filters.stockLevel)?.label}
                <button onClick={() => handleFilterChange('stockLevel', 'all')}>×</button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
