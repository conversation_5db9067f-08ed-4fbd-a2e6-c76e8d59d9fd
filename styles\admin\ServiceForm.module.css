.serviceForm {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.formHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  margin: -1rem -1rem 2rem -1rem;
}

.formHeader h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.error {
  background: #fee;
  border: 1px solid #fcc;
  color: #c33;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.success {
  background: #efe;
  border: 1px solid #cfc;
  color: #3c3;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.form {
  padding: 0 2rem 2rem 2rem;
}

.section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.section:last-of-type {
  border-bottom: none;
  margin-bottom: 0;
}

.section h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.formRow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  font-weight: 500;
  color: #555;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input,
.select,
.textarea {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.input:focus,
.select:focus,
.textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.colorInput {
  width: 60px;
  height: 40px;
  padding: 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.checkboxLabel {
  flex-direction: row !important;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.checkboxLabel input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.cancelButton,
.saveButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background: #f5f5f5;
  color: #666;
}

.cancelButton:hover {
  background: #e5e5e5;
}

.saveButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.saveButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.saveButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Image upload section */
.imageUpload {
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.imageUpload:hover {
  border-color: #667eea;
}

.imageUpload.dragOver {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.uploadText {
  color: #666;
  font-size: 0.9rem;
}

.imagePreview {
  margin-top: 1rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.previewImage {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

/* Responsive design */
@media (max-width: 768px) {
  .serviceForm {
    margin: 0;
    border-radius: 0;
  }

  .formHeader {
    margin: -1rem -1rem 1rem -1rem;
    padding: 1rem;
  }

  .form {
    padding: 0 1rem 1rem 1rem;
  }

  .formRow {
    grid-template-columns: 1fr;
  }

  .formActions {
    flex-direction: column;
  }

  .cancelButton,
  .saveButton {
    width: 100%;
  }
}

/* Additional styling for better UX */
.requiredField {
  color: #e74c3c;
}

.helpText {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.25rem;
}

.fieldError {
  border-color: #e74c3c !important;
}

.fieldError:focus {
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
}

.loadingSpinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
