import { supabaseAdmin } from '@/lib/supabase';
import * as authTokenManager from '@/lib/auth-token-manager';

/**
 * Admin Customer Individual Operations API
 * 
 * GET /api/admin/customers/{id} - Get customer details with enhanced data
 * PUT /api/admin/customers/{id} - Update customer information
 * DELETE /api/admin/customers/{id} - Delete customer (admin only)
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Admin customer API request: ${req.method} ${req.url}`);

  // Authenticate request using the auth token manager
  const authResult = await authTokenManager.verifyToken(req);

  if (!authResult.valid) {
    console.error(`[${requestId}] Authentication failed:`, authResult.error);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authResult.error || 'Authentication failed',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${authResult.user?.email}, Role: ${authResult.user?.role}`);

  const { id } = req.query;

  if (!id) {
    return res.status(400).json({
      error: 'Customer ID is required',
      requestId
    });
  }

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getCustomer(id, res, requestId)
    case 'PUT':
      return updateCustomer(id, req, res, requestId)
    case 'DELETE':
      // Only admins can delete customers
      if (authResult.user?.role !== 'admin') {
        console.error(`[${requestId}] Delete forbidden for role: ${authResult.user?.role}`);
        return res.status(403).json({
          error: 'Forbidden',
          message: 'Only administrators can delete customers',
          requestId
        });
      }
      return deleteCustomer(id, res, requestId)
    default:
      return res.status(405).json({
        error: 'Method not allowed',
        requestId
      })
  }
}

// Get customer with enhanced admin data
async function getCustomer(id, res, requestId) {
  try {
    console.log(`[${requestId}] Fetching enhanced customer data: ${id}`);

    // Get customer from analytics summary view for enhanced data
    const { data: customer, error } = await supabaseAdmin
      .from('customer_analytics_summary')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`[${requestId}] Error fetching customer:`, error);
      if (error.code === 'PGRST116') {
        return res.status(404).json({
          error: 'Customer not found',
          requestId
        });
      }
      throw error;
    }

    // Get customer tags
    const { data: tags, error: tagsError } = await supabaseAdmin
      .from('customer_tag_assignments')
      .select(`
        id,
        assigned_at,
        notes,
        customer_tags (
          id,
          name,
          description,
          color,
          category,
          is_system
        )
      `)
      .eq('customer_id', id);

    if (tagsError) {
      console.error(`[${requestId}] Error fetching customer tags:`, tagsError);
      // Don't fail the request, just log the error
    }

    // Transform tags data
    const customerTags = tags?.map(assignment => ({
      assignmentId: assignment.id,
      assignedAt: assignment.assigned_at,
      notes: assignment.notes,
      ...assignment.customer_tags
    })) || [];

    console.log(`[${requestId}] Successfully fetched enhanced customer data with ${customerTags.length} tags`);

    return res.status(200).json({
      customer: {
        ...customer,
        tags: customerTags
      },
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error fetching customer:`, error);
    return res.status(500).json({
      error: 'Failed to fetch customer',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while fetching customer data',
      requestId
    });
  }
}

// Update customer with enhanced fields
async function updateCustomer(id, req, res, requestId) {
  try {
    console.log(`[${requestId}] Updating customer: ${id}`);

    const {
      name,
      email,
      phone,
      address,
      city,
      state,
      postal_code,
      country,
      notes,
      marketing_consent,
      customer_status,
      customer_tier,
      acquisition_source,
      preferred_communication_method,
      communication_frequency
    } = req.body;

    // Validate required fields
    if (!name || !email) {
      console.log(`[${requestId}] Validation failed: missing name or email`);
      return res.status(400).json({
        error: 'Name and email are required',
        requestId
      });
    }

    // Check if email already exists (for a different customer)
    const { data: existingCustomer, error: checkError } = await supabaseAdmin
      .from('customers')
      .select('id')
      .eq('email', email)
      .neq('id', id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error(`[${requestId}] Error checking existing customer:`, checkError);
      throw checkError;
    }

    if (existingCustomer) {
      console.log(`[${requestId}] Email conflict: ${email} already exists for customer ${existingCustomer.id}`);
      return res.status(409).json({
        error: 'Another customer with this email already exists',
        requestId
      });
    }

    // Prepare update data
    const updateData = {
      name,
      email,
      phone,
      address,
      city,
      state,
      postal_code,
      country,
      notes,
      marketing_consent,
      updated_at: new Date()
    };

    // Add enhanced fields if provided
    if (customer_status) updateData.customer_status = customer_status;
    if (customer_tier) updateData.customer_tier = customer_tier;
    if (acquisition_source) updateData.acquisition_source = acquisition_source;
    if (preferred_communication_method) updateData.preferred_communication_method = preferred_communication_method;
    if (communication_frequency) updateData.communication_frequency = communication_frequency;

    // Update customer
    const { data, error } = await supabaseAdmin
      .from('customers')
      .update(updateData)
      .eq('id', id)
      .select();

    if (error) {
      console.error(`[${requestId}] Error updating customer:`, error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.log(`[${requestId}] Customer not found during update: ${id}`);
      return res.status(404).json({
        error: 'Customer not found',
        requestId
      });
    }

    console.log(`[${requestId}] Customer updated successfully: ${data[0].name}`);
    return res.status(200).json({
      customer: data[0],
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error updating customer:`, error);
    return res.status(500).json({
      error: 'Failed to update customer',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while updating the customer',
      requestId
    });
  }
}

// Delete customer
async function deleteCustomer(id, res, requestId) {
  try {
    console.log(`[${requestId}] Deleting customer: ${id}`);

    // Delete customer (related data will be deleted via cascade)
    const { data, error } = await supabaseAdmin
      .from('customers')
      .delete()
      .eq('id', id)
      .select();

    if (error) {
      console.error(`[${requestId}] Error deleting customer:`, error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.log(`[${requestId}] Customer not found for deletion: ${id}`);
      return res.status(404).json({
        error: 'Customer not found',
        requestId
      });
    }

    console.log(`[${requestId}] Customer deleted successfully: ${data[0].name}`);
    return res.status(200).json({
      message: 'Customer deleted successfully',
      customer: data[0],
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Error deleting customer:`, error);
    return res.status(500).json({
      error: 'Failed to delete customer',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while deleting the customer',
      requestId
    });
  }
}
