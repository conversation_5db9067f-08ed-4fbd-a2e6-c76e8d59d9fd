# Admin Inventory Management Quick Start Implementation Guide

## Immediate Actions (This Week)

### 1. Database Setup
```bash
# Execute the inventory system enhancements migration
cd your-project-directory
psql -h your-supabase-host -U postgres -d postgres -f db/migrations/inventory_system_enhancements.sql
```

### 2. Priority 1 Components to Build

#### Advanced Inventory Search Component
**File**: `components/admin/AdvancedInventorySearch.js`
**Estimated Time**: 6-8 hours
**Key Features**:
- Multi-criteria search with debounced input
- Advanced filtering by stock levels, price, cost, supplier
- Saved filter presets
- Real-time search results with stock status indicators

#### Automated Reorder Management System
**File**: `components/admin/ReorderRulesManager.js`
**Estimated Time**: 8-10 hours
**Key Features**:
- AI-powered reorder point recommendations
- Economic Order Quantity (EOQ) calculations
- Automated purchase order generation
- Supplier performance integration

#### Enhanced Inventory Analytics Dashboard
**File**: `components/admin/EnhancedInventoryAnalytics.js`
**Estimated Time**: 10-12 hours
**Key Features**:
- Comprehensive inventory KPIs and metrics
- ABC analysis and turnover calculations
- Cost analysis and profitability tracking
- Demand forecasting and trend analysis

### 3. API Endpoints to Create

#### Advanced Inventory Search
**File**: `pages/api/admin/inventory/search.js`
```javascript
// GET /api/admin/inventory/search
// Query params: search, category, stockLevel, priceRange, costRange, supplier, etc.
export default async function handler(req, res) {
  const {
    search = '',
    category = 'all',
    stockLevel = 'all',
    priceRange = {},
    costRange = {},
    supplier = 'all',
    abcClassification = 'all',
    limit = 50,
    offset = 0
  } = req.query;

  try {
    let query = supabase
      .from('inventory_summary')
      .select('*')
      .order('name', { ascending: true })
      .range(offset, offset + limit - 1);

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,sku.ilike.%${search}%,barcode.ilike.%${search}%`);
    }

    // Apply category filter
    if (category !== 'all') {
      query = query.eq('category', category);
    }

    // Apply stock level filter
    if (stockLevel !== 'all') {
      query = query.eq('stock_status', stockLevel);
    }

    // Apply price range filter
    if (priceRange.min) {
      query = query.gte('price', priceRange.min);
    }
    if (priceRange.max) {
      query = query.lte('price', priceRange.max);
    }

    // Apply cost range filter
    if (costRange.min) {
      query = query.gte('cost_price', costRange.min);
    }
    if (costRange.max) {
      query = query.lte('cost_price', costRange.max);
    }

    // Apply supplier filter
    if (supplier !== 'all') {
      query = query.eq('supplier_id', supplier);
    }

    // Apply ABC classification filter
    if (abcClassification !== 'all') {
      query = query.eq('abc_classification', abcClassification);
    }

    const { data, error, count } = await query;

    if (error) throw error;

    res.status(200).json({
      products: data,
      total: count,
      hasMore: offset + limit < count
    });
  } catch (error) {
    console.error('Inventory search error:', error);
    res.status(500).json({ error: 'Failed to search inventory' });
  }
}
```

#### Reorder Suggestions API
**File**: `pages/api/admin/inventory/reorder-suggestions/[id].js`
```javascript
// GET /api/admin/inventory/reorder-suggestions/[id]
export default async function handler(req, res) {
  const { id } = req.query;

  try {
    // Get product and inventory data
    const { data: product, error: productError } = await supabase
      .from('products')
      .select(`
        *,
        inventory(*),
        suppliers(*)
      `)
      .eq('id', id)
      .single();

    if (productError) throw productError;

    // Get sales history for demand calculation
    const { data: salesHistory, error: salesError } = await supabase
      .from('order_items')
      .select(`
        quantity,
        orders(created_at)
      `)
      .eq('product_id', id)
      .gte('orders.created_at', new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString())
      .order('orders.created_at', { ascending: false });

    if (salesError) throw salesError;

    // Calculate demand metrics
    const totalSales = salesHistory.reduce((sum, item) => sum + item.quantity, 0);
    const averageDailyDemand = totalSales / 365;
    const leadTimeDays = product.suppliers?.lead_time_days || 7;

    // Calculate reorder point (Lead time demand + Safety stock)
    const leadTimeDemand = averageDailyDemand * leadTimeDays;
    const demandVariability = calculateDemandVariability(salesHistory);
    const safetyStock = Math.ceil(demandVariability * Math.sqrt(leadTimeDays));
    const recommendedReorderPoint = Math.ceil(leadTimeDemand + safetyStock);

    // Calculate Economic Order Quantity (EOQ)
    const annualDemand = totalSales;
    const orderingCost = 50; // Estimated ordering cost
    const holdingCostRate = 0.25; // 25% of item cost
    const holdingCost = (product.cost_price || product.price * 0.6) * holdingCostRate;

    const economicOrderQuantity = Math.ceil(
      Math.sqrt((2 * annualDemand * orderingCost) / holdingCost)
    );

    const suggestions = {
      recommended_reorder_point: recommendedReorderPoint,
      economic_order_quantity: economicOrderQuantity,
      recommended_safety_stock: safetyStock,
      average_daily_demand: averageDailyDemand.toFixed(2),
      analysis_period: 365,
      lead_time_demand: Math.ceil(leadTimeDemand),
      current_stock: product.inventory?.quantity || 0,
      days_of_stock: averageDailyDemand > 0 ? Math.floor((product.inventory?.quantity || 0) / averageDailyDemand) : null
    };

    res.status(200).json({ suggestions });
  } catch (error) {
    console.error('Reorder suggestions error:', error);
    res.status(500).json({ error: 'Failed to calculate reorder suggestions' });
  }
}

function calculateDemandVariability(salesHistory) {
  if (salesHistory.length < 2) return 0;

  const dailySales = {};
  salesHistory.forEach(item => {
    const date = new Date(item.orders.created_at).toDateString();
    dailySales[date] = (dailySales[date] || 0) + item.quantity;
  });

  const salesValues = Object.values(dailySales);
  const mean = salesValues.reduce((sum, val) => sum + val, 0) / salesValues.length;
  const variance = salesValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / salesValues.length;

  return Math.sqrt(variance);
}
```

#### Inventory Analytics API
**File**: `pages/api/admin/analytics/inventory.js`
```javascript
// GET /api/admin/analytics/inventory
export default async function handler(req, res) {
  const { range = '90d' } = req.query;

  try {
    const days = parseInt(range.replace('d', ''));
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Get inventory summary
    const { data: inventorySummary, error: summaryError } = await supabase
      .from('inventory_summary')
      .select('*');

    if (summaryError) throw summaryError;

    // Calculate key metrics
    const totalProducts = inventorySummary.length;
    const totalInventoryValue = inventorySummary.reduce((sum, item) => sum + (item.inventory_value || 0), 0);
    const lowStockCount = inventorySummary.filter(item => item.stock_status === 'low_stock').length;
    const outOfStockCount = inventorySummary.filter(item => item.stock_status === 'out_of_stock').length;

    // Get turnover analysis
    const { data: turnoverData, error: turnoverError } = await supabase
      .from('inventory_turnover')
      .select('*')
      .order('turnover_ratio', { ascending: false });

    if (turnoverError) throw turnoverError;

    const averageTurnoverRatio = turnoverData.reduce((sum, item) => sum + (item.turnover_ratio || 0), 0) / turnoverData.length;

    // Get ABC analysis
    const { data: abcData, error: abcError } = await supabase
      .from('abc_analysis')
      .select('*');

    if (abcError) throw abcError;

    // Get alerts
    const { data: alerts, error: alertsError } = await supabase
      .from('inventory_alerts')
      .select('*')
      .eq('is_resolved', false)
      .order('severity', { ascending: false })
      .order('created_at', { ascending: false });

    if (alertsError) throw alertsError;

    const analytics = {
      totalProducts,
      totalInventoryValue,
      lowStockCount,
      outOfStockCount,
      inventoryTurnoverRatio: averageTurnoverRatio.toFixed(2),
      stockoutRate: ((outOfStockCount / totalProducts) * 100).toFixed(1),
      alerts: alerts.map(alert => ({
        ...alert,
        severity: alert.severity,
        type: alert.alert_type
      })),
      abcDistribution: {
        A: abcData.filter(item => item.suggested_abc_class === 'A').length,
        B: abcData.filter(item => item.suggested_abc_class === 'B').length,
        C: abcData.filter(item => item.suggested_abc_class === 'C').length
      },
      topPerformers: turnoverData.slice(0, 10),
      slowMovers: turnoverData.filter(item => item.turnover_ratio < 2).slice(0, 10)
    };

    res.status(200).json(analytics);
  } catch (error) {
    console.error('Inventory analytics error:', error);
    res.status(500).json({ error: 'Failed to fetch inventory analytics' });
  }
}
```

## Week 1 Implementation Checklist

### Day 1-2: Database & Backend Setup
- [ ] Run inventory system enhancements migration
- [ ] Test new database tables and functions
- [ ] Create inventory search API endpoint
- [ ] Create reorder suggestions API
- [ ] Create inventory analytics API
- [ ] Update existing inventory API to include new fields

### Day 3-4: Core Components
- [ ] Build AdvancedInventorySearch component
- [ ] Create ReorderRulesManager component
- [ ] Update InventoryList to use new search functionality
- [ ] Add bulk operations to inventory list
- [ ] Implement stock level indicators and alerts

### Day 5: Analytics & Integration
- [ ] Build EnhancedInventoryAnalytics component
- [ ] Integrate inventory analytics display
- [ ] Add cost analysis and profitability tracking
- [ ] Test all new functionality
- [ ] Fix integration issues

## Code Templates

### 1. Inventory Search Hook
```javascript
// hooks/useInventorySearch.js
import { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';

export const useInventorySearch = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    category: 'all',
    stockLevel: 'all',
    supplier: 'all'
  });
  const [pagination, setPagination] = useState({
    offset: 0,
    limit: 50,
    total: 0,
    hasMore: false
  });

  const debouncedSearch = useCallback(
    debounce(async (searchFilters) => {
      try {
        setLoading(true);
        setError(null);

        const queryParams = new URLSearchParams({
          ...searchFilters,
          offset: 0,
          limit: pagination.limit
        });

        const response = await fetch(`/api/admin/inventory/search?${queryParams}`);
        const data = await response.json();

        if (!response.ok) throw new Error(data.error);

        setProducts(data.products);
        setPagination(prev => ({
          ...prev,
          offset: 0,
          total: data.total,
          hasMore: data.hasMore
        }));
      } catch (error) {
        setError(error.message);
        console.error('Inventory search error:', error);
      } finally {
        setLoading(false);
      }
    }, 300),
    [pagination.limit]
  );

  useEffect(() => {
    debouncedSearch(filters);
  }, [filters, debouncedSearch]);

  const updateFilters = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const loadMore = async () => {
    if (!pagination.hasMore || loading) return;

    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        ...filters,
        offset: pagination.offset + pagination.limit,
        limit: pagination.limit
      });

      const response = await fetch(`/api/admin/inventory/search?${queryParams}`);
      const data = await response.json();

      if (!response.ok) throw new Error(data.error);

      setProducts(prev => [...prev, ...data.products]);
      setPagination(prev => ({
        ...prev,
        offset: prev.offset + prev.limit,
        hasMore: data.hasMore
      }));
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return {
    products,
    loading,
    error,
    filters,
    pagination,
    updateFilters,
    loadMore
  };
};
```

### 2. Inventory Analytics Utilities
```javascript
// utils/inventoryAnalytics.js
export const calculateInventoryMetrics = (inventoryData) => {
  const totalValue = inventoryData.reduce((sum, item) => sum + (item.inventory_value || 0), 0);
  const lowStockItems = inventoryData.filter(item => item.stock_status === 'low_stock');
  const outOfStockItems = inventoryData.filter(item => item.stock_status === 'out_of_stock');

  return {
    totalProducts: inventoryData.length,
    totalValue,
    lowStockCount: lowStockItems.length,
    outOfStockCount: outOfStockItems.length,
    stockoutRate: (outOfStockItems.length / inventoryData.length) * 100,
    averageStockLevel: inventoryData.reduce((sum, item) => sum + (item.quantity || 0), 0) / inventoryData.length
  };
};

export const calculateTurnoverRatio = (salesData, averageInventory) => {
  const totalSales = salesData.reduce((sum, sale) => sum + sale.quantity, 0);
  return averageInventory > 0 ? totalSales / averageInventory : 0;
};

export const calculateReorderPoint = (averageDailyDemand, leadTimeDays, safetyStock = 0) => {
  return Math.ceil((averageDailyDemand * leadTimeDays) + safetyStock);
};

export const calculateEOQ = (annualDemand, orderingCost, holdingCost) => {
  return Math.ceil(Math.sqrt((2 * annualDemand * orderingCost) / holdingCost));
};

export const getStockStatusColor = (status) => {
  const colors = {
    'in_stock': '#4CAF50',
    'low_stock': '#FF9800',
    'out_of_stock': '#F44336',
    'overstocked': '#2196F3'
  };
  return colors[status] || '#757575';
};

export const formatCurrency = (amount, currency = 'AUD') => {
  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: currency
  }).format(amount);
};
```

### 3. Bulk Operations Component
```javascript
// components/admin/InventoryBulkOperations.js
import { useState } from 'react';
import styles from '@/styles/admin/InventoryBulkOperations.module.css';

export default function InventoryBulkOperations({
  selectedProducts,
  onBulkAction,
  onClearSelection
}) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);

  const bulkActions = [
    { value: 'update_prices', label: 'Update Prices', icon: '💰' },
    { value: 'adjust_stock', label: 'Adjust Stock', icon: '📦' },
    { value: 'set_reorder_rules', label: 'Set Reorder Rules', icon: '🔄' },
    { value: 'update_supplier', label: 'Update Supplier', icon: '🏭' },
    { value: 'export', label: 'Export Selected', icon: '📊' },
    { value: 'generate_labels', label: 'Generate Labels', icon: '🏷️' }
  ];

  const handleBulkAction = (action) => {
    if (['adjust_stock', 'update_prices'].includes(action)) {
      setPendingAction(action);
      setShowConfirmation(true);
    } else {
      onBulkAction(action, selectedProducts);
    }
  };

  return (
    <div className={styles.bulkOperations}>
      <div className={styles.selectionInfo}>
        <span className={styles.count}>
          {selectedProducts.length} product{selectedProducts.length !== 1 ? 's' : ''} selected
        </span>
        <button
          onClick={onClearSelection}
          className={styles.clearSelection}
        >
          Clear Selection
        </button>
      </div>

      <div className={styles.actions}>
        {bulkActions.map(action => (
          <button
            key={action.value}
            onClick={() => handleBulkAction(action.value)}
            className={styles.actionButton}
            disabled={selectedProducts.length === 0}
          >
            <span className={styles.actionIcon}>{action.icon}</span>
            {action.label}
          </button>
        ))}
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className={styles.confirmationModal}>
          <div className={styles.modalContent}>
            <h3>Confirm Bulk Action</h3>
            <p>
              Are you sure you want to {pendingAction.replace('_', ' ')} for {selectedProducts.length} product(s)?
            </p>
            <div className={styles.modalActions}>
              <button
                onClick={() => setShowConfirmation(false)}
                className={styles.cancelButton}
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  onBulkAction(pendingAction, selectedProducts);
                  setShowConfirmation(false);
                  setPendingAction(null);
                }}
                className={styles.confirmButton}
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
```

## Testing Checklist

### Functionality Tests
- [ ] Advanced search returns accurate results
- [ ] Filtering works correctly for all criteria
- [ ] Reorder suggestions are calculated properly
- [ ] Inventory analytics display correct metrics
- [ ] Bulk operations execute successfully
- [ ] Stock level alerts trigger appropriately

### Performance Tests
- [ ] Search results return in <1 second
- [ ] Inventory list loads quickly with 1000+ products
- [ ] Analytics calculations complete in reasonable time
- [ ] Database queries are optimized

### User Experience Tests
- [ ] Search interface is intuitive
- [ ] Inventory analytics provide valuable insights
- [ ] Reorder management is easy to use
- [ ] Mobile responsiveness maintained

## Success Metrics for Week 1

### Technical Metrics
- [ ] All new API endpoints respond in <500ms
- [ ] Database migration completes without errors
- [ ] Search functionality returns results in <1 second
- [ ] All tests pass

### User Experience Metrics
- [ ] Inventory search time reduced by 70%
- [ ] Reorder point calculations provide accurate recommendations
- [ ] Analytics dashboard provides actionable insights
- [ ] Bulk operations save 80% of time vs individual actions

### Business Metrics
- [ ] Time to access inventory information reduced by 60%
- [ ] Automated reorder suggestions improve stock management
- [ ] Cost analysis identifies optimization opportunities
- [ ] Inventory accuracy improves through better tracking

## Common Issues & Solutions

### Issue 1: Slow Search Performance
**Solution**: Ensure database indexes are created:
```sql
CREATE INDEX products_search_idx ON products USING gin(to_tsvector('english', name || ' ' || sku));
```

### Issue 2: Reorder Calculations Timeout
**Solution**: Implement caching for demand calculations:
```javascript
const cachedDemandData = await redis.get(`demand_${productId}`);
if (!cachedDemandData) {
  const demandData = await calculateDemand(productId);
  await redis.setex(`demand_${productId}`, 3600, JSON.stringify(demandData));
}
```

### Issue 3: Analytics Loading Slowly
**Solution**: Use materialized views for complex analytics:
```sql
CREATE MATERIALIZED VIEW inventory_analytics_cache AS
SELECT * FROM inventory_summary;
REFRESH MATERIALIZED VIEW inventory_analytics_cache;
```

## Next Week Preview

### Week 2 Focus Areas
1. **Supplier Performance Tracking**: Implement comprehensive supplier analytics
2. **Demand Forecasting**: Add predictive analytics for inventory planning
3. **Cost Optimization**: Build cost analysis and margin optimization tools
4. **Mobile Inventory Management**: Optimize for mobile devices and add barcode scanning

### Preparation Tasks
- [ ] Research demand forecasting algorithms
- [ ] Plan supplier performance metrics
- [ ] Design cost optimization workflows
- [ ] Set up mobile testing environment

## Integration with Booking System

### Service Inventory Tracking
```javascript
// Track inventory usage for services
const trackServiceInventoryUsage = async (bookingId, serviceId) => {
  const { data: serviceItems } = await supabase
    .from('service_inventory_items')
    .select('product_id, quantity_used')
    .eq('service_id', serviceId);

  for (const item of serviceItems) {
    await supabase
      .from('inventory_transactions')
      .insert({
        product_id: item.product_id,
        quantity: -item.quantity_used,
        transaction_type: 'out',
        reference_type: 'booking',
        reference_id: bookingId,
        notes: `Used for service in booking ${bookingId}`
      });
  }
};
```

This quick start guide provides everything needed to begin immediate implementation of the most impactful inventory management system improvements, with clear integration points to the existing booking system.
