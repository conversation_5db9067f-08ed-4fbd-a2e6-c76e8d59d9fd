# Admin Inventory Management Implementation Examples

## 1. Advanced Inventory Search Component

### Enhanced Search Interface
```jsx
// components/admin/AdvancedInventorySearch.js
import { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import styles from '@/styles/admin/AdvancedInventorySearch.module.css';

export default function AdvancedInventorySearch({ onFiltersChange, onProductsUpdate }) {
  const [filters, setFilters] = useState({
    search: '',
    category: 'all',
    stockLevel: 'all', // 'in_stock', 'low_stock', 'out_of_stock', 'overstocked'
    priceRange: { min: '', max: '' },
    costRange: { min: '', max: '' },
    supplier: 'all',
    lastRestocked: '',
    abcClassification: 'all',
    location: 'all'
  });

  const [savedFilters, setSavedFilters] = useState([]);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [categories, setCategories] = useState([]);
  const [suppliers, setSuppliers] = useState([]);

  const stockLevelOptions = [
    { value: 'all', label: 'All Stock Levels' },
    { value: 'in_stock', label: 'In Stock' },
    { value: 'low_stock', label: 'Low Stock' },
    { value: 'out_of_stock', label: 'Out of Stock' },
    { value: 'overstocked', label: 'Overstocked' }
  ];

  const abcClassificationOptions = [
    { value: 'all', label: 'All Classifications' },
    { value: 'A', label: 'Class A (High Value)' },
    { value: 'B', label: 'Class B (Medium Value)' },
    { value: 'C', label: 'Class C (Low Value)' }
  ];

  const lastRestockedOptions = [
    { value: '', label: 'Any time' },
    { value: '7', label: 'Last 7 days' },
    { value: '30', label: 'Last 30 days' },
    { value: '90', label: 'Last 90 days' },
    { value: '365', label: 'Last year' }
  ];

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchFilters) => {
      onFiltersChange(searchFilters);
    }, 300),
    [onFiltersChange]
  );

  useEffect(() => {
    debouncedSearch(filters);
  }, [filters, debouncedSearch]);

  useEffect(() => {
    fetchCategories();
    fetchSuppliers();
    loadSavedFilters();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/inventory/categories');
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchSuppliers = async () => {
    try {
      const response = await fetch('/api/admin/suppliers');
      const data = await response.json();
      setSuppliers(data.suppliers || []);
    } catch (error) {
      console.error('Error fetching suppliers:', error);
    }
  };

  const loadSavedFilters = () => {
    const saved = localStorage.getItem('inventoryFilters');
    if (saved) {
      setSavedFilters(JSON.parse(saved));
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleNestedFilterChange = (parentKey, childKey, value) => {
    setFilters(prev => ({
      ...prev,
      [parentKey]: {
        ...prev[parentKey],
        [childKey]: value
      }
    }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      category: 'all',
      stockLevel: 'all',
      priceRange: { min: '', max: '' },
      costRange: { min: '', max: '' },
      supplier: 'all',
      lastRestocked: '',
      abcClassification: 'all',
      location: 'all'
    });
  };

  const saveCurrentFilters = () => {
    const filterName = prompt('Enter a name for this filter set:');
    if (filterName) {
      const newSavedFilter = {
        id: Date.now(),
        name: filterName,
        filters: { ...filters }
      };
      const updatedFilters = [...savedFilters, newSavedFilter];
      setSavedFilters(updatedFilters);
      localStorage.setItem('inventoryFilters', JSON.stringify(updatedFilters));
    }
  };

  const loadSavedFilter = (savedFilter) => {
    setFilters(savedFilter.filters);
  };

  const hasActiveFilters = () => {
    return filters.search ||
           filters.category !== 'all' ||
           filters.stockLevel !== 'all' ||
           filters.priceRange.min ||
           filters.priceRange.max ||
           filters.costRange.min ||
           filters.costRange.max ||
           filters.supplier !== 'all' ||
           filters.lastRestocked ||
           filters.abcClassification !== 'all';
  };

  return (
    <div className={styles.searchContainer}>
      {/* Quick Search Bar */}
      <div className={styles.quickSearch}>
        <div className={styles.searchInputGroup}>
          <input
            type="text"
            placeholder="Search products by name, SKU, barcode..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className={styles.searchInput}
          />
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={styles.advancedToggle}
          >
            Advanced {showAdvanced ? '▲' : '▼'}
          </button>
        </div>

        <div className={styles.quickFilters}>
          <select
            value={filters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className={styles.quickSelect}
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>

          <select
            value={filters.stockLevel}
            onChange={(e) => handleFilterChange('stockLevel', e.target.value)}
            className={styles.quickSelect}
          >
            {stockLevelOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className={styles.advancedFilters}>
          <div className={styles.filterSection}>
            <h4>Price Range</h4>
            <div className={styles.rangeInputs}>
              <input
                type="number"
                placeholder="Min price"
                value={filters.priceRange.min}
                onChange={(e) => handleNestedFilterChange('priceRange', 'min', e.target.value)}
                className={styles.rangeInput}
              />
              <span>to</span>
              <input
                type="number"
                placeholder="Max price"
                value={filters.priceRange.max}
                onChange={(e) => handleNestedFilterChange('priceRange', 'max', e.target.value)}
                className={styles.rangeInput}
              />
            </div>
          </div>

          <div className={styles.filterSection}>
            <h4>Cost Range</h4>
            <div className={styles.rangeInputs}>
              <input
                type="number"
                placeholder="Min cost"
                value={filters.costRange.min}
                onChange={(e) => handleNestedFilterChange('costRange', 'min', e.target.value)}
                className={styles.rangeInput}
              />
              <span>to</span>
              <input
                type="number"
                placeholder="Max cost"
                value={filters.costRange.max}
                onChange={(e) => handleNestedFilterChange('costRange', 'max', e.target.value)}
                className={styles.rangeInput}
              />
            </div>
          </div>

          <div className={styles.filterSection}>
            <h4>Supplier</h4>
            <select
              value={filters.supplier}
              onChange={(e) => handleFilterChange('supplier', e.target.value)}
              className={styles.select}
            >
              <option value="all">All Suppliers</option>
              {suppliers.map(supplier => (
                <option key={supplier.id} value={supplier.id}>
                  {supplier.name}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.filterSection}>
            <h4>Last Restocked</h4>
            <select
              value={filters.lastRestocked}
              onChange={(e) => handleFilterChange('lastRestocked', e.target.value)}
              className={styles.select}
            >
              {lastRestockedOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.filterSection}>
            <h4>ABC Classification</h4>
            <select
              value={filters.abcClassification}
              onChange={(e) => handleFilterChange('abcClassification', e.target.value)}
              className={styles.select}
            >
              {abcClassificationOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.filterActions}>
            <button onClick={clearFilters} className={styles.clearButton}>
              Clear All Filters
            </button>
            <button onClick={saveCurrentFilters} className={styles.saveButton}>
              Save Filter Set
            </button>
          </div>
        </div>
      )}

      {/* Saved Filters */}
      {savedFilters.length > 0 && (
        <div className={styles.savedFilters}>
          <h4>Saved Filters:</h4>
          <div className={styles.savedFiltersList}>
            {savedFilters.map(savedFilter => (
              <button
                key={savedFilter.id}
                onClick={() => loadSavedFilter(savedFilter)}
                className={styles.savedFilterButton}
              >
                {savedFilter.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters() && (
        <div className={styles.activeFilters}>
          <span className={styles.activeFiltersLabel}>Active Filters:</span>
          <div className={styles.activeFilterTags}>
            {filters.search && (
              <span className={styles.filterTag}>
                Search: "{filters.search}"
                <button onClick={() => handleFilterChange('search', '')}>×</button>
              </span>
            )}
            {filters.category !== 'all' && (
              <span className={styles.filterTag}>
                Category: {categories.find(c => c.id === filters.category)?.name}
                <button onClick={() => handleFilterChange('category', 'all')}>×</button>
              </span>
            )}
            {filters.stockLevel !== 'all' && (
              <span className={styles.filterTag}>
                Stock: {stockLevelOptions.find(s => s.value === filters.stockLevel)?.label}
                <button onClick={() => handleFilterChange('stockLevel', 'all')}>×</button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
```

## 2. Automated Reorder Management System

### Reorder Rules Component
```jsx
// components/admin/ReorderRulesManager.js
import { useState, useEffect } from 'react';
import styles from '@/styles/admin/ReorderRulesManager.module.css';

export default function ReorderRulesManager({ productId, onRulesUpdate }) {
  const [reorderRule, setReorderRule] = useState({
    reorder_point: '',
    reorder_quantity: '',
    max_stock_level: '',
    lead_time_days: 7,
    safety_stock: 0,
    is_active: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [suggestions, setSuggestions] = useState(null);

  useEffect(() => {
    if (productId) {
      fetchReorderRule();
      fetchSuggestions();
    }
  }, [productId]);

  const fetchReorderRule = async () => {
    try {
      const response = await fetch(`/api/admin/inventory/reorder-rules/${productId}`);
      if (response.ok) {
        const data = await response.json();
        if (data.rule) {
          setReorderRule(data.rule);
        }
      }
    } catch (error) {
      console.error('Error fetching reorder rule:', error);
    }
  };

  const fetchSuggestions = async () => {
    try {
      const response = await fetch(`/api/admin/inventory/reorder-suggestions/${productId}`);
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions);
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setReorderRule(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const applySuggestion = (suggestionType) => {
    if (!suggestions) return;

    switch (suggestionType) {
      case 'reorder_point':
        setReorderRule(prev => ({
          ...prev,
          reorder_point: suggestions.recommended_reorder_point
        }));
        break;
      case 'reorder_quantity':
        setReorderRule(prev => ({
          ...prev,
          reorder_quantity: suggestions.economic_order_quantity
        }));
        break;
      case 'safety_stock':
        setReorderRule(prev => ({
          ...prev,
          safety_stock: suggestions.recommended_safety_stock
        }));
        break;
    }
  };

  const saveReorderRule = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/admin/inventory/reorder-rules/${productId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(reorderRule)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save reorder rule');
      }

      onRulesUpdate && onRulesUpdate();
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.reorderManager}>
      <h3>Automated Reorder Management</h3>

      {error && <div className={styles.error}>{error}</div>}

      {/* Suggestions Panel */}
      {suggestions && (
        <div className={styles.suggestionsPanel}>
          <h4>AI Recommendations</h4>
          <div className={styles.suggestionsList}>
            <div className={styles.suggestion}>
              <span className={styles.suggestionLabel}>Reorder Point:</span>
              <span className={styles.suggestionValue}>
                {suggestions.recommended_reorder_point} units
              </span>
              <button
                onClick={() => applySuggestion('reorder_point')}
                className={styles.applySuggestion}
              >
                Apply
              </button>
            </div>
            <div className={styles.suggestion}>
              <span className={styles.suggestionLabel}>Economic Order Quantity:</span>
              <span className={styles.suggestionValue}>
                {suggestions.economic_order_quantity} units
              </span>
              <button
                onClick={() => applySuggestion('reorder_quantity')}
                className={styles.applySuggestion}
              >
                Apply
              </button>
            </div>
            <div className={styles.suggestion}>
              <span className={styles.suggestionLabel}>Safety Stock:</span>
              <span className={styles.suggestionValue}>
                {suggestions.recommended_safety_stock} units
              </span>
              <button
                onClick={() => applySuggestion('safety_stock')}
                className={styles.applySuggestion}
              >
                Apply
              </button>
            </div>
          </div>
          <div className={styles.suggestionReason}>
            <small>
              Based on {suggestions.analysis_period} days of sales data,
              average demand: {suggestions.average_daily_demand} units/day
            </small>
          </div>
        </div>
      )}

      {/* Reorder Rule Form */}
      <div className={styles.reorderForm}>
        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label>Reorder Point</label>
            <input
              type="number"
              value={reorderRule.reorder_point}
              onChange={(e) => handleInputChange('reorder_point', parseInt(e.target.value))}
              className={styles.input}
            />
            <small>Trigger reorder when stock reaches this level</small>
          </div>
          <div className={styles.formGroup}>
            <label>Reorder Quantity</label>
            <input
              type="number"
              value={reorderRule.reorder_quantity}
              onChange={(e) => handleInputChange('reorder_quantity', parseInt(e.target.value))}
              className={styles.input}
            />
            <small>Quantity to order when reorder point is reached</small>
          </div>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label>Maximum Stock Level</label>
            <input
              type="number"
              value={reorderRule.max_stock_level}
              onChange={(e) => handleInputChange('max_stock_level', parseInt(e.target.value))}
              className={styles.input}
            />
            <small>Maximum inventory level to maintain</small>
          </div>
          <div className={styles.formGroup}>
            <label>Lead Time (Days)</label>
            <input
              type="number"
              value={reorderRule.lead_time_days}
              onChange={(e) => handleInputChange('lead_time_days', parseInt(e.target.value))}
              className={styles.input}
            />
            <small>Expected delivery time from supplier</small>
          </div>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label>Safety Stock</label>
            <input
              type="number"
              value={reorderRule.safety_stock}
              onChange={(e) => handleInputChange('safety_stock', parseInt(e.target.value))}
              className={styles.input}
            />
            <small>Buffer stock for demand variability</small>
          </div>
          <div className={styles.formGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={reorderRule.is_active}
                onChange={(e) => handleInputChange('is_active', e.target.checked)}
              />
              Enable Automated Reordering
            </label>
          </div>
        </div>

        <div className={styles.formActions}>
          <button
            onClick={saveReorderRule}
            disabled={loading}
            className={styles.saveButton}
          >
            {loading ? 'Saving...' : 'Save Reorder Rules'}
          </button>
        </div>
      </div>
    </div>
  );
}
```

## 3. Enhanced Inventory Analytics Dashboard

### Comprehensive Analytics Component
```jsx
// components/admin/EnhancedInventoryAnalytics.js
import { useState, useEffect } from 'react';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import styles from '@/styles/admin/EnhancedInventoryAnalytics.module.css';

export default function EnhancedInventoryAnalytics({ timeRange = '90d' }) {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState('overview');

  const metrics = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'turnover', label: 'Turnover Analysis', icon: '🔄' },
    { id: 'abc_analysis', label: 'ABC Analysis', icon: '🎯' },
    { id: 'cost_analysis', label: 'Cost Analysis', icon: '💰' },
    { id: 'forecasting', label: 'Demand Forecasting', icon: '📈' },
    { id: 'supplier_performance', label: 'Supplier Performance', icon: '🏭' }
  ];

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/analytics/inventory?range=${timeRange}`);
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Error fetching inventory analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className={styles.loading}>Loading analytics...</div>;
  }

  return (
    <div className={styles.inventoryAnalytics}>
      <div className={styles.analyticsHeader}>
        <h2>Inventory Analytics</h2>
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className={styles.timeRangeSelector}
        >
          <option value="30d">Last 30 Days</option>
          <option value="90d">Last 90 Days</option>
          <option value="180d">Last 6 Months</option>
          <option value="365d">Last Year</option>
        </select>
      </div>

      <div className={styles.metricsNavigation}>
        {metrics.map(metric => (
          <button
            key={metric.id}
            onClick={() => setSelectedMetric(metric.id)}
            className={`${styles.metricTab} ${selectedMetric === metric.id ? styles.active : ''}`}
          >
            <span className={styles.metricIcon}>{metric.icon}</span>
            {metric.label}
          </button>
        ))}
      </div>

      <div className={styles.analyticsContent}>
        {selectedMetric === 'overview' && (
          <InventoryOverviewAnalytics analytics={analytics} />
        )}
        {selectedMetric === 'turnover' && (
          <InventoryTurnoverAnalytics analytics={analytics} />
        )}
        {selectedMetric === 'abc_analysis' && (
          <ABCAnalysisComponent analytics={analytics} />
        )}
        {selectedMetric === 'cost_analysis' && (
          <CostAnalysisComponent analytics={analytics} />
        )}
        {selectedMetric === 'forecasting' && (
          <DemandForecastingComponent analytics={analytics} />
        )}
        {selectedMetric === 'supplier_performance' && (
          <SupplierPerformanceAnalytics analytics={analytics} />
        )}
      </div>
    </div>
  );
}

// Overview Analytics Sub-component
function InventoryOverviewAnalytics({ analytics }) {
  return (
    <div className={styles.overviewAnalytics}>
      <div className={styles.kpiGrid}>
        <div className={styles.kpiCard}>
          <div className={styles.kpiValue}>${analytics?.totalInventoryValue || 0}</div>
          <div className={styles.kpiLabel}>Total Inventory Value</div>
          <div className={styles.kpiChange}>
            {analytics?.inventoryValueChange > 0 ? '+' : ''}
            {analytics?.inventoryValueChange}% vs last period
          </div>
        </div>
        <div className={styles.kpiCard}>
          <div className={styles.kpiValue}>{analytics?.inventoryTurnoverRatio || 0}</div>
          <div className={styles.kpiLabel}>Inventory Turnover Ratio</div>
          <div className={styles.kpiChange}>
            Target: {analytics?.targetTurnoverRatio || 6}x annually
          </div>
        </div>
        <div className={styles.kpiCard}>
          <div className={styles.kpiValue}>{analytics?.daysOfInventory || 0}</div>
          <div className={styles.kpiLabel}>Days of Inventory</div>
          <div className={styles.kpiChange}>
            {analytics?.daysOfInventoryChange > 0 ? '+' : ''}
            {analytics?.daysOfInventoryChange} days vs last period
          </div>
        </div>
        <div className={styles.kpiCard}>
          <div className={styles.kpiValue}>{analytics?.stockoutRate || 0}%</div>
          <div className={styles.kpiLabel}>Stockout Rate</div>
          <div className={styles.kpiChange}>
            Target: <5%
          </div>
        </div>
      </div>

      <div className={styles.chartsGrid}>
        <div className={styles.chartCard}>
          <h3>Inventory Value Trend</h3>
          <Line data={prepareInventoryValueTrendData(analytics?.valueTrend)} />
        </div>
        <div className={styles.chartCard}>
          <h3>Stock Level Distribution</h3>
          <Doughnut data={prepareStockLevelDistributionData(analytics?.stockDistribution)} />
        </div>
      </div>

      <div className={styles.alertsSection}>
        <h3>Inventory Alerts</h3>
        <div className={styles.alertsList}>
          {analytics?.alerts?.map(alert => (
            <div key={alert.id} className={`${styles.alert} ${styles[alert.severity]}`}>
              <span className={styles.alertIcon}>{getAlertIcon(alert.type)}</span>
              <div className={styles.alertContent}>
                <div className={styles.alertTitle}>{alert.title}</div>
                <div className={styles.alertMessage}>{alert.message}</div>
              </div>
              <div className={styles.alertActions}>
                <button className={styles.alertAction}>View</button>
                <button className={styles.alertAction}>Resolve</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
```

## 4. Cost Tracking and Profitability Analysis

### Cost Analysis Component
```jsx
// components/admin/CostAnalysisComponent.js
import { useState, useEffect } from 'react';
import { Line, Bar } from 'react-chartjs-2';
import styles from '@/styles/admin/CostAnalysisComponent.module.css';

export default function CostAnalysisComponent({ productId, analytics }) {
  const [costData, setCostData] = useState(null);
  const [selectedCostType, setSelectedCostType] = useState('all');
  const [loading, setLoading] = useState(false);

  const costTypes = [
    { value: 'all', label: 'All Costs' },
    { value: 'purchase', label: 'Purchase Cost' },
    { value: 'landed', label: 'Landed Cost' },
    { value: 'overhead', label: 'Overhead Cost' },
    { value: 'labor', label: 'Labor Cost' }
  ];

  useEffect(() => {
    if (productId) {
      fetchCostData();
    }
  }, [productId, selectedCostType]);

  const fetchCostData = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/admin/inventory/cost-analysis/${productId}?type=${selectedCostType}`
      );
      const data = await response.json();
      setCostData(data);
    } catch (error) {
      console.error('Error fetching cost data:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateMarginAnalysis = () => {
    if (!costData) return null;

    const totalCost = costData.totalCost;
    const sellingPrice = costData.sellingPrice;
    const grossMargin = sellingPrice - totalCost;
    const marginPercentage = (grossMargin / sellingPrice) * 100;

    return {
      totalCost,
      sellingPrice,
      grossMargin,
      marginPercentage,
      breakEvenQuantity: Math.ceil(costData.fixedCosts / grossMargin)
    };
  };

  const marginAnalysis = calculateMarginAnalysis();

  return (
    <div className={styles.costAnalysis}>
      <div className={styles.costHeader}>
        <h3>Cost Analysis & Profitability</h3>
        <select
          value={selectedCostType}
          onChange={(e) => setSelectedCostType(e.target.value)}
          className={styles.costTypeSelector}
        >
          {costTypes.map(type => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>
      </div>

      {loading ? (
        <div className={styles.loading}>Loading cost analysis...</div>
      ) : (
        <>
          {/* Cost Breakdown */}
          <div className={styles.costBreakdown}>
            <h4>Cost Breakdown</h4>
            <div className={styles.costItems}>
              {costData?.costBreakdown?.map(item => (
                <div key={item.type} className={styles.costItem}>
                  <span className={styles.costLabel}>{item.label}</span>
                  <span className={styles.costValue}>${item.amount}</span>
                  <span className={styles.costPercentage}>
                    {((item.amount / costData.totalCost) * 100).toFixed(1)}%
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Margin Analysis */}
          {marginAnalysis && (
            <div className={styles.marginAnalysis}>
              <h4>Margin Analysis</h4>
              <div className={styles.marginGrid}>
                <div className={styles.marginCard}>
                  <div className={styles.marginValue}>${marginAnalysis.totalCost}</div>
                  <div className={styles.marginLabel}>Total Cost</div>
                </div>
                <div className={styles.marginCard}>
                  <div className={styles.marginValue}>${marginAnalysis.sellingPrice}</div>
                  <div className={styles.marginLabel}>Selling Price</div>
                </div>
                <div className={styles.marginCard}>
                  <div className={styles.marginValue}>${marginAnalysis.grossMargin}</div>
                  <div className={styles.marginLabel}>Gross Margin</div>
                </div>
                <div className={styles.marginCard}>
                  <div className={styles.marginValue}>
                    {marginAnalysis.marginPercentage.toFixed(1)}%
                  </div>
                  <div className={styles.marginLabel}>Margin %</div>
                </div>
              </div>
            </div>
          )}

          {/* Cost Trend Chart */}
          <div className={styles.costTrendChart}>
            <h4>Cost Trend Analysis</h4>
            <Line data={prepareCostTrendData(costData?.costTrend)} />
          </div>

          {/* Cost Optimization Recommendations */}
          <div className={styles.recommendations}>
            <h4>Cost Optimization Recommendations</h4>
            <div className={styles.recommendationsList}>
              {costData?.recommendations?.map(rec => (
                <div key={rec.id} className={styles.recommendation}>
                  <div className={styles.recIcon}>{rec.icon}</div>
                  <div className={styles.recContent}>
                    <div className={styles.recTitle}>{rec.title}</div>
                    <div className={styles.recDescription}>{rec.description}</div>
                    <div className={styles.recImpact}>
                      Potential savings: ${rec.potentialSavings}
                    </div>
                  </div>
                  <button className={styles.recAction}>Implement</button>
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
```
