import { useState, useEffect } from 'react';
import styles from '@/styles/admin/EnhancedInventoryAnalytics.module.css';

export default function EnhancedInventoryAnalytics({ timeRange = '90d' }) {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedMetric, setSelectedMetric] = useState('overview');

  const metrics = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'alerts', label: 'Alerts', icon: '⚠️' },
    { id: 'abc_analysis', label: 'ABC Analysis', icon: '🎯' },
    { id: 'top_performers', label: 'Top Performers', icon: '🏆' },
    { id: 'needs_attention', label: 'Needs Attention', icon: '🔍' }
  ];

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/admin/analytics/inventory?range=${timeRange}`);
      const data = await response.json();
      
      if (response.ok) {
        setAnalytics(data);
      } else {
        throw new Error(data.error || 'Failed to fetch analytics');
      }
    } catch (error) {
      console.error('Error fetching inventory analytics:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading analytics...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.error}>
        <p>Error loading analytics: {error}</p>
        <button onClick={fetchAnalytics} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={styles.inventoryAnalytics}>
      <div className={styles.analyticsHeader}>
        <h2>Inventory Analytics</h2>
        <div className={styles.timeRangeSelector}>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className={styles.timeSelect}
          >
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
            <option value="180d">Last 6 Months</option>
            <option value="365d">Last Year</option>
          </select>
        </div>
      </div>

      <div className={styles.metricsNavigation}>
        {metrics.map(metric => (
          <button
            key={metric.id}
            onClick={() => setSelectedMetric(metric.id)}
            className={`${styles.metricTab} ${selectedMetric === metric.id ? styles.active : ''}`}
          >
            <span className={styles.metricIcon}>{metric.icon}</span>
            {metric.label}
          </button>
        ))}
      </div>

      <div className={styles.analyticsContent}>
        {selectedMetric === 'overview' && (
          <InventoryOverviewAnalytics analytics={analytics} />
        )}
        {selectedMetric === 'alerts' && (
          <InventoryAlertsAnalytics analytics={analytics} />
        )}
        {selectedMetric === 'abc_analysis' && (
          <ABCAnalysisComponent analytics={analytics} />
        )}
        {selectedMetric === 'top_performers' && (
          <TopPerformersAnalytics analytics={analytics} />
        )}
        {selectedMetric === 'needs_attention' && (
          <NeedsAttentionAnalytics analytics={analytics} />
        )}
      </div>
    </div>
  );
}

// Overview Analytics Sub-component
function InventoryOverviewAnalytics({ analytics }) {
  const summary = analytics?.summary || {};
  
  return (
    <div className={styles.overviewAnalytics}>
      <div className={styles.kpiGrid}>
        <div className={styles.kpiCard}>
          <div className={styles.kpiValue}>${summary.totalInventoryValue?.toLocaleString() || 0}</div>
          <div className={styles.kpiLabel}>Total Inventory Value</div>
          <div className={styles.kpiSubtext}>Across {summary.totalProducts || 0} products</div>
        </div>
        
        <div className={styles.kpiCard}>
          <div className={styles.kpiValue}>{summary.inventoryTurnoverRatio || 0}x</div>
          <div className={styles.kpiLabel}>Inventory Turnover Ratio</div>
          <div className={styles.kpiSubtext}>Annual turnover rate</div>
        </div>
        
        <div className={styles.kpiCard}>
          <div className={styles.kpiValue}>{summary.stockoutRate || 0}%</div>
          <div className={styles.kpiLabel}>Stockout Rate</div>
          <div className={styles.kpiSubtext}>{summary.outOfStockCount || 0} products out of stock</div>
        </div>
        
        <div className={styles.kpiCard}>
          <div className={styles.kpiValue}>{summary.lowStockRate || 0}%</div>
          <div className={styles.kpiLabel}>Low Stock Rate</div>
          <div className={styles.kpiSubtext}>{summary.lowStockCount || 0} products low on stock</div>
        </div>
      </div>

      <div className={styles.chartsGrid}>
        <div className={styles.chartCard}>
          <h3>Stock Level Distribution</h3>
          <StockDistributionChart data={analytics?.stockDistribution} />
        </div>
        
        <div className={styles.chartCard}>
          <h3>Key Insights</h3>
          <InsightsList insights={analytics?.insights} />
        </div>
      </div>
    </div>
  );
}

// Alerts Analytics Sub-component
function InventoryAlertsAnalytics({ analytics }) {
  const alerts = analytics?.alerts || [];
  
  const getSeverityColor = (severity) => {
    const colors = {
      'critical': '#dc3545',
      'high': '#fd7e14',
      'medium': '#ffc107',
      'low': '#28a745'
    };
    return colors[severity] || '#6c757d';
  };

  const getAlertIcon = (type) => {
    const icons = {
      'low_stock': '📦',
      'out_of_stock': '❌',
      'overstock': '📈',
      'reorder_needed': '🔄',
      'expiry_warning': '⏰',
      'slow_moving': '🐌'
    };
    return icons[type] || '⚠️';
  };

  return (
    <div className={styles.alertsAnalytics}>
      <div className={styles.alertsHeader}>
        <h3>Active Inventory Alerts ({alerts.length})</h3>
      </div>
      
      {alerts.length === 0 ? (
        <div className={styles.noAlerts}>
          <div className={styles.noAlertsIcon}>✅</div>
          <p>No active alerts! Your inventory is in good shape.</p>
        </div>
      ) : (
        <div className={styles.alertsList}>
          {alerts.map(alert => (
            <div 
              key={alert.id} 
              className={styles.alertItem}
              style={{ borderLeftColor: getSeverityColor(alert.severity) }}
            >
              <div className={styles.alertIcon}>
                {getAlertIcon(alert.type)}
              </div>
              <div className={styles.alertContent}>
                <div className={styles.alertTitle}>{alert.title}</div>
                <div className={styles.alertMessage}>{alert.message}</div>
                <div className={styles.alertMeta}>
                  <span className={styles.alertSeverity} style={{ color: getSeverityColor(alert.severity) }}>
                    {alert.severity.toUpperCase()}
                  </span>
                  <span className={styles.alertTime}>
                    {new Date(alert.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
              <div className={styles.alertActions}>
                <button className={styles.alertAction}>View Product</button>
                <button className={styles.alertAction}>Resolve</button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// ABC Analysis Sub-component
function ABCAnalysisComponent({ analytics }) {
  const abcData = analytics?.abcDistribution || {};
  const total = Object.values(abcData).reduce((sum, count) => sum + count, 0);

  return (
    <div className={styles.abcAnalysis}>
      <div className={styles.abcHeader}>
        <h3>ABC Classification Analysis</h3>
        <p>Products categorized by value contribution</p>
      </div>
      
      <div className={styles.abcGrid}>
        {Object.entries(abcData).map(([classification, count]) => {
          const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : 0;
          const getClassInfo = (cls) => {
            const info = {
              'A': { color: '#28a745', description: 'High value items (80% of revenue)' },
              'B': { color: '#ffc107', description: 'Medium value items (15% of revenue)' },
              'C': { color: '#6c757d', description: 'Low value items (5% of revenue)' },
              'Unclassified': { color: '#dc3545', description: 'Items needing classification' }
            };
            return info[cls] || { color: '#6c757d', description: 'Unknown classification' };
          };
          
          const classInfo = getClassInfo(classification);
          
          return (
            <div key={classification} className={styles.abcCard}>
              <div 
                className={styles.abcIndicator}
                style={{ backgroundColor: classInfo.color }}
              ></div>
              <div className={styles.abcContent}>
                <div className={styles.abcClass}>Class {classification}</div>
                <div className={styles.abcCount}>{count} products</div>
                <div className={styles.abcPercentage}>{percentage}%</div>
                <div className={styles.abcDescription}>{classInfo.description}</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Top Performers Sub-component
function TopPerformersAnalytics({ analytics }) {
  const topPerformers = analytics?.topPerformers || [];

  return (
    <div className={styles.topPerformers}>
      <div className={styles.performersHeader}>
        <h3>Top Performing Products</h3>
        <p>Highest inventory value products</p>
      </div>
      
      {topPerformers.length === 0 ? (
        <div className={styles.noData}>
          <p>No performance data available</p>
        </div>
      ) : (
        <div className={styles.performersList}>
          {topPerformers.map((product, index) => (
            <div key={product.id} className={styles.performerItem}>
              <div className={styles.performerRank}>#{index + 1}</div>
              <div className={styles.performerInfo}>
                <div className={styles.performerName}>{product.name}</div>
                <div className={styles.performerSku}>SKU: {product.sku}</div>
              </div>
              <div className={styles.performerMetrics}>
                <div className={styles.performerValue}>
                  ${product.inventory_value?.toLocaleString() || 0}
                </div>
                <div className={styles.performerQuantity}>
                  {product.quantity} units
                </div>
                <div className={`${styles.performerStatus} ${styles[product.stock_status]}`}>
                  {product.stock_status?.replace('_', ' ')}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Needs Attention Sub-component
function NeedsAttentionAnalytics({ analytics }) {
  const needsAttention = analytics?.needsAttention || [];

  return (
    <div className={styles.needsAttention}>
      <div className={styles.attentionHeader}>
        <h3>Products Needing Attention</h3>
        <p>High-value products with stock issues</p>
      </div>
      
      {needsAttention.length === 0 ? (
        <div className={styles.noIssues}>
          <div className={styles.noIssuesIcon}>👍</div>
          <p>All high-value products are well-stocked!</p>
        </div>
      ) : (
        <div className={styles.attentionList}>
          {needsAttention.map(product => (
            <div key={product.id} className={styles.attentionItem}>
              <div className={styles.attentionInfo}>
                <div className={styles.attentionName}>{product.name}</div>
                <div className={styles.attentionSku}>SKU: {product.sku}</div>
                <div className={styles.attentionValue}>
                  Value: ${product.inventory_value?.toLocaleString() || 0}
                </div>
              </div>
              <div className={styles.attentionMetrics}>
                <div className={styles.attentionStock}>
                  Current: {product.quantity} units
                </div>
                <div className={styles.attentionThreshold}>
                  Threshold: {product.low_stock_threshold} units
                </div>
                <div className={`${styles.attentionStatus} ${styles[product.stock_status]}`}>
                  {product.stock_status?.replace('_', ' ')}
                </div>
              </div>
              <div className={styles.attentionActions}>
                <button className={styles.attentionAction}>Reorder</button>
                <button className={styles.attentionAction}>View Details</button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Simple Stock Distribution Chart Component
function StockDistributionChart({ data }) {
  const stockData = data || {};
  const total = Object.values(stockData).reduce((sum, count) => sum + count, 0);

  return (
    <div className={styles.stockChart}>
      {Object.entries(stockData).map(([status, count]) => {
        const percentage = total > 0 ? ((count / total) * 100).toFixed(1) : 0;
        const getStatusColor = (status) => {
          const colors = {
            'in_stock': '#28a745',
            'low_stock': '#ffc107',
            'out_of_stock': '#dc3545',
            'overstocked': '#17a2b8'
          };
          return colors[status] || '#6c757d';
        };

        return (
          <div key={status} className={styles.stockItem}>
            <div 
              className={styles.stockIndicator}
              style={{ backgroundColor: getStatusColor(status) }}
            ></div>
            <div className={styles.stockLabel}>
              {status.replace('_', ' ').toUpperCase()}
            </div>
            <div className={styles.stockCount}>{count}</div>
            <div className={styles.stockPercentage}>{percentage}%</div>
          </div>
        );
      })}
    </div>
  );
}

// Insights List Component
function InsightsList({ insights }) {
  const insightsList = insights || [];

  const getInsightIcon = (type) => {
    const icons = {
      'warning': '⚠️',
      'info': 'ℹ️',
      'success': '✅',
      'error': '❌'
    };
    return icons[type] || 'ℹ️';
  };

  const getInsightColor = (priority) => {
    const colors = {
      'high': '#dc3545',
      'medium': '#ffc107',
      'low': '#28a745'
    };
    return colors[priority] || '#6c757d';
  };

  return (
    <div className={styles.insightsList}>
      {insightsList.length === 0 ? (
        <div className={styles.noInsights}>
          <p>No insights available</p>
        </div>
      ) : (
        insightsList.map((insight, index) => (
          <div key={index} className={styles.insightItem}>
            <div className={styles.insightIcon}>
              {getInsightIcon(insight.type)}
            </div>
            <div className={styles.insightContent}>
              <div className={styles.insightTitle}>{insight.title}</div>
              <div className={styles.insightMessage}>{insight.message}</div>
              <div className={styles.insightAction}>{insight.action}</div>
            </div>
            <div 
              className={styles.insightPriority}
              style={{ color: getInsightColor(insight.priority) }}
            >
              {insight.priority?.toUpperCase()}
            </div>
          </div>
        ))
      )}
    </div>
  );
}
