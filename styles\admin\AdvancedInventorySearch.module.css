.searchContainer {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quickSearch {
  margin-bottom: 15px;
}

.searchInputGroup {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  position: relative;
}

.searchInput {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.searchInput:focus {
  outline: none;
  border-color: #0066cc;
}

.advancedToggle {
  padding: 12px 20px;
  background: #f8f9fa;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  white-space: nowrap;
}

.advancedToggle:hover {
  background: #e9ecef;
}

.advancedToggle.active {
  background: #0066cc;
  color: white;
  border-color: #0066cc;
}

.loadingIndicator {
  position: absolute;
  right: 120px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #666;
  font-style: italic;
}

.quickFilters {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.quickSelect {
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 150px;
}

.quickSelect:focus {
  outline: none;
  border-color: #0066cc;
}

.advancedFilters {
  border-top: 1px solid #e1e5e9;
  padding-top: 20px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filterRow {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.filterSection {
  flex: 1;
  min-width: 200px;
}

.filterSection label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.rangeInputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rangeInput {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
}

.rangeInput:focus {
  outline: none;
  border-color: #0066cc;
}

.rangeInputs span {
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.select:focus {
  outline: none;
  border-color: #0066cc;
}

.filterActions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid #e1e5e9;
}

.clearButton {
  padding: 10px 20px;
  background: #f8f9fa;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.clearButton:hover {
  background: #e9ecef;
}

.saveButton {
  padding: 10px 20px;
  background: #28a745;
  color: white;
  border: 2px solid #28a745;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.saveButton:hover {
  background: #218838;
  border-color: #218838;
}

.savedFilters {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e1e5e9;
}

.savedFilters h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.savedFiltersList {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.savedFilterItem {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 20px;
  overflow: hidden;
}

.savedFilterButton {
  padding: 6px 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 12px;
  color: #333;
  transition: background 0.2s;
}

.savedFilterButton:hover {
  background: #e9ecef;
}

.deleteSavedFilter {
  padding: 6px 8px;
  background: none;
  border: none;
  cursor: pointer;
  color: #dc3545;
  font-size: 14px;
  font-weight: bold;
  transition: background 0.2s;
}

.deleteSavedFilter:hover {
  background: #dc3545;
  color: white;
}

.activeFilters {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e1e5e9;
}

.activeFiltersLabel {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 10px;
}

.activeFilterTags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.filterTag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.filterTag button {
  background: none;
  border: none;
  color: #1976d2;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  padding: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.filterTag button:hover {
  background: #1976d2;
  color: white;
}

/* Responsive design */
@media (max-width: 768px) {
  .searchContainer {
    padding: 15px;
  }
  
  .searchInputGroup {
    flex-direction: column;
    align-items: stretch;
  }
  
  .quickFilters {
    flex-direction: column;
  }
  
  .quickSelect {
    min-width: auto;
  }
  
  .filterRow {
    flex-direction: column;
    gap: 15px;
  }
  
  .filterSection {
    min-width: auto;
  }
  
  .filterActions {
    flex-direction: column;
  }
  
  .activeFilterTags {
    flex-direction: column;
    align-items: flex-start;
  }
}
